import cv2
import numpy as np
import re
from PIL import Image

try:
    from paddleocr import PaddleOCR
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False
    print("警告: PaddleOCR未安装，将使用简化识别模式")

class LicensePlateRecognizer:
    def __init__(self):
        # 中国车牌号码正则表达式
        self.license_pattern = re.compile(
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]'
            r'[A-Z]'
            r'[A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]'
        )

        # 初始化OCR引擎
        if PADDLEOCR_AVAILABLE:
            try:
                self.ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
                self.ocr_available = True
                print("PaddleOCR初始化成功")
            except Exception as e:
                print(f"PaddleOCR初始化失败: {e}")
                self.ocr_available = False
        else:
            self.ocr_available = False

    def preprocess_image(self, image):
        """图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 边缘检测
        edges = cv2.Canny(blurred, 50, 150)

        return gray, edges

    def find_license_plate_contours(self, edges):
        """查找车牌轮廓"""
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 筛选可能的车牌轮廓
        license_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 1000:  # 面积太小，跳过
                continue

            # 获取轮廓的边界矩形
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h

            # 车牌的宽高比通常在2-5之间
            if 2 < aspect_ratio < 5:
                license_contours.append((contour, x, y, w, h))

        return license_contours

    def extract_text_from_region(self, image, x, y, w, h):
        """从指定区域提取文本"""
        if not self.ocr_available:
            return ""

        try:
            # 提取车牌区域
            plate_region = image[y:y+h, x:x+w]

            # 使用PaddleOCR识别文本
            result = self.ocr.ocr(plate_region, cls=True)

            if result and result[0]:
                texts = []
                for line in result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    if confidence > 0.5:  # 置信度阈值
                        texts.append(text)

                return ' '.join(texts)

            return ""
        except Exception as e:
            print(f"文本提取错误: {e}")
            return ""

    def clean_license_plate_text(self, text):
        """清理和验证车牌号码"""
        # 移除空格和特殊字符
        cleaned = re.sub(r'[^\u4e00-\u9fa5A-Z0-9]', '', text.upper())

        # 查找符合车牌格式的文本
        matches = self.license_pattern.findall(cleaned)
        if matches:
            return matches[0]

        # 如果没有完全匹配，尝试部分匹配
        if len(cleaned) >= 7:
            return cleaned[:8]  # 取前8位

        return cleaned

    def recognize_from_image(self, image_path):
        """从图片文件识别车牌"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return None, "无法读取图像文件"

            return self.recognize_from_array(image)

        except Exception as e:
            return None, f"识别过程中出错: {str(e)}"

    def recognize_from_array(self, image):
        """从图像数组识别车牌"""
        try:
            if not self.ocr_available:
                # 简化模式：返回提示信息，让用户手动输入
                return "手动输入", "PaddleOCR未安装，请手动输入车牌号"

            # 直接使用PaddleOCR识别整个图像
            result = self.ocr.ocr(image, cls=True)

            if result and result[0]:
                all_text = ""
                for line in result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    if confidence > 0.5:
                        all_text += text + " "

                # 清理和验证车牌号码
                license_plate = self.clean_license_plate_text(all_text)

                if license_plate and len(license_plate) >= 6:
                    return license_plate, "识别成功"
                else:
                    return None, "未能识别出有效的车牌号码"

            return None, "未检测到文本"

        except Exception as e:
            return None, f"识别过程中出错: {str(e)}"

    def recognize_from_camera(self, camera_index=0):
        """从摄像头实时识别车牌"""
        if not self.ocr_available:
            return "手动输入", "PaddleOCR未安装，请手动输入车牌号"

        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            return None, "无法打开摄像头"

        try:
            ret, frame = cap.read()
            if ret:
                license_plate, message = self.recognize_from_array(frame)
                cap.release()
                return license_plate, message
            else:
                cap.release()
                return None, "无法从摄像头获取图像"

        except Exception as e:
            cap.release()
            return None, f"摄像头识别出错: {str(e)}"


