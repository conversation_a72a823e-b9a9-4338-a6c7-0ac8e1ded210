import cv2
import numpy as np
import re
from PIL import Image
import time

# 尝试导入OCR库（延迟加载）
OCR_ENGINE = None
OCR_AVAILABLE = False

print("🔧 车牌识别模块加载中...")

class LicensePlateRecognizer:
    def __init__(self):
        # 中国车牌号码正则表达式
        self.license_pattern = re.compile(
            r'[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]'
            r'[A-Z]'
            r'[A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]'
        )

        # OCR引擎状态
        self.ocr_available = False
        self.ocr_engine = None
        self.ocr = None
        self.ocr_init_attempted = False

        print("✅ 车牌识别器初始化完成（支持图像分析和手动输入）")

    def try_init_ocr(self):
        """尝试初始化OCR引擎（延迟加载）"""
        if self.ocr_init_attempted:
            return self.ocr_available

        self.ocr_init_attempted = True

        # 尝试EasyOCR
        try:
            print("🔄 尝试初始化EasyOCR...")
            import easyocr
            self.ocr = easyocr.Reader(['ch_sim', 'en'], gpu=False)
            self.ocr_engine = "easyocr"
            self.ocr_available = True
            print("✅ EasyOCR初始化成功！")
            return True
        except Exception as e:
            print(f"⚠️ EasyOCR初始化失败: {e}")

        # 尝试PaddleOCR
        try:
            print("🔄 尝试初始化PaddleOCR...")
            from paddleocr import PaddleOCR
            self.ocr = PaddleOCR(use_angle_cls=True, lang='ch', show_log=False)
            self.ocr_engine = "paddleocr"
            self.ocr_available = True
            print("✅ PaddleOCR初始化成功！")
            return True
        except Exception as e:
            print(f"⚠️ PaddleOCR初始化失败: {e}")

        print("⚠️ 所有OCR引擎初始化失败，将使用图像分析模式")
        return False

    def preprocess_image(self, image):
        """图像预处理"""
        # 转换为灰度图
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image

        # 高斯模糊
        blurred = cv2.GaussianBlur(gray, (5, 5), 0)

        # 边缘检测
        edges = cv2.Canny(blurred, 50, 150)

        return gray, edges

    def find_license_plate_contours(self, edges):
        """查找车牌轮廓"""
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 筛选可能的车牌轮廓
        license_contours = []
        for contour in contours:
            area = cv2.contourArea(contour)
            if area < 1000:  # 面积太小，跳过
                continue

            # 获取轮廓的边界矩形
            x, y, w, h = cv2.boundingRect(contour)
            aspect_ratio = w / h

            # 车牌的宽高比通常在2-5之间
            if 2 < aspect_ratio < 5:
                license_contours.append((contour, x, y, w, h))

        return license_contours

    def extract_text_from_region(self, image, x, y, w, h):
        """从指定区域提取文本"""
        if not self.ocr_available:
            return ""

        try:
            # 提取车牌区域
            plate_region = image[y:y+h, x:x+w]

            # 使用PaddleOCR识别文本
            result = self.ocr.ocr(plate_region, cls=True)

            if result and result[0]:
                texts = []
                for line in result[0]:
                    text = line[1][0]
                    confidence = line[1][1]
                    if confidence > 0.5:  # 置信度阈值
                        texts.append(text)

                return ' '.join(texts)

            return ""
        except Exception as e:
            print(f"文本提取错误: {e}")
            return ""

    def clean_license_plate_text(self, text):
        """清理和验证车牌号码"""
        # 移除空格和特殊字符
        cleaned = re.sub(r'[^\u4e00-\u9fa5A-Z0-9]', '', text.upper())

        # 查找符合车牌格式的文本
        matches = self.license_pattern.findall(cleaned)
        if matches:
            return matches[0]

        # 如果没有完全匹配，尝试部分匹配
        if len(cleaned) >= 7:
            return cleaned[:8]  # 取前8位

        return cleaned

    def simple_license_recognition(self, image):
        """简单的车牌识别方法（基于图像处理）"""
        try:
            # 预处理图像
            _, edges = self.preprocess_image(image)

            # 查找车牌轮廓
            license_contours = self.find_license_plate_contours(edges)

            if license_contours:
                # 检测到可能的车牌区域，返回提示让用户手动输入
                print(f"🔍 检测到{len(license_contours)}个可能的车牌区域")
                return "手动输入"  # 特殊标识，表示需要用户手动输入

            return None

        except Exception as e:
            print(f"简单识别出错: {e}")
            return None

    def recognize_from_image(self, image_path):
        """从图片文件识别车牌"""
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                return None, "无法读取图像文件"

            return self.recognize_from_array(image)

        except Exception as e:
            return None, f"识别过程中出错: {str(e)}"

    def recognize_from_array(self, image):
        """从图像数组识别车牌"""
        try:
            # 首先尝试初始化并使用OCR引擎
            if self.try_init_ocr():
                print(f"🔍 正在使用{self.ocr_engine}识别车牌，请稍候...")

                try:
                    if self.ocr_engine == "easyocr":
                        # 使用EasyOCR识别
                        results = self.ocr.readtext(image)

                        all_text = ""
                        high_confidence_text = ""

                        for (_, text, confidence) in results:
                            all_text += text + " "
                            if confidence > 0.6:  # EasyOCR置信度阈值
                                high_confidence_text += text + " "

                        # 优先使用高置信度文本
                        text_to_process = high_confidence_text if high_confidence_text.strip() else all_text

                    elif self.ocr_engine == "paddleocr":
                        # 使用PaddleOCR识别
                        result = self.ocr.ocr(image, cls=True)

                        if result and result[0]:
                            all_text = ""
                            high_confidence_text = ""
                            for line in result[0]:
                                text = line[1][0]
                                confidence = line[1][1]
                                all_text += text + " "
                                if confidence > 0.7:  # PaddleOCR置信度阈值
                                    high_confidence_text += text + " "

                            text_to_process = high_confidence_text if high_confidence_text.strip() else all_text
                        else:
                            text_to_process = ""

                    if text_to_process.strip():
                        # 清理和验证车牌号码
                        license_plate = self.clean_license_plate_text(text_to_process)

                        if license_plate and len(license_plate) >= 6:
                            print(f"✅ 识别成功: {license_plate}")
                            return license_plate, f"识别成功: {license_plate}"
                        else:
                            print(f"⚠️ 识别到文本但格式不符: {text_to_process.strip()}")
                            return None, f"识别到文本但不是有效车牌格式: {text_to_process.strip()}"

                except Exception as ocr_error:
                    print(f"⚠️ OCR识别失败: {ocr_error}")
                    # OCR失败时继续使用简单识别

            # OCR不可用或失败时，使用简单的图像处理方法
            print("🔍 使用简单图像处理方法分析车牌...")
            license_plate = self.simple_license_recognition(image)

            if license_plate == "手动输入":
                print("🎯 检测到车牌区域，请手动输入车牌号")
                return None, "检测到车牌区域，请手动输入车牌号"
            elif license_plate:
                print(f"✅ 识别成功: {license_plate}")
                return license_plate, f"识别成功: {license_plate}"
            else:
                print("❌ 未检测到车牌区域")
                return None, "未检测到车牌区域，请确保图片清晰且包含完整车牌，或手动输入车牌号"

        except Exception as e:
            print(f"❌ 识别过程出错: {e}")
            return None, f"识别过程中出错: {str(e)}"

    def recognize_from_camera(self, camera_index=0):
        """从摄像头实时识别车牌"""
        print("📷 正在打开摄像头进行车牌识别...")
        cap = cv2.VideoCapture(camera_index)

        if not cap.isOpened():
            print("❌ 无法打开摄像头")
            return None, "无法打开摄像头，请检查摄像头连接或权限"

        try:
            print("📸 正在拍摄...")
            ret, frame = cap.read()
            if ret:
                license_plate, message = self.recognize_from_array(frame)
                cap.release()
                return license_plate, message
            else:
                cap.release()
                print("❌ 无法从摄像头获取图像")
                return None, "无法从摄像头获取图像"

        except Exception as e:
            cap.release()
            print(f"❌ 摄像头识别出错: {e}")
            return None, f"摄像头识别出错: {str(e)}"


