# 写字楼停车管理系统 - 完整版

## 项目概述

这是一个完整的、可投入生产使用的写字楼停车管理系统，基于Python Flask框架开发，连接MySQL数据库，实现了您要求的所有核心功能。

## ✅ 已实现的核心功能

### 1. 车牌识别模块
- **手动上传识别**：支持JPG、PNG等格式图片上传，使用PaddleOCR进行车牌识别
- **实时摄像头识别**：调用设备摄像头进行实时车牌识别
- **智能算法**：基于PaddleOCR的高精度中文车牌识别引擎

### 2. 停车收费管理
- **自动计费**：按停车时长自动计算费用
- **灵活配置**：可设置每小时费用、免费时间、日最高限额
- **会员优惠**：支持会员折扣价格（如8折、9折等）

### 3. 管理员功能
- **安全登录**：基于Flask-Login的管理员认证系统
- **价格设置**：完整的价格配置界面，支持普通用户和会员价格
- **手动操作**：支持手动车辆入库/出库操作
- **系统管理**：完整的系统配置和管理功能

### 4. 支付功能
- **多种支付方式**：微信支付、支付宝支付
- **二维码支付**：管理员可手动上传微信和支付宝收款二维码
- **支付确认**：完整的支付流程和状态管理

### 5. 统计报表
- **实时统计**：今日入场/出场数量、当前在场车辆、今日收入等
- **图表展示**：收入趋势图、停车次数统计、支付方式分布等
- **数据导出**：支持Excel和CSV格式数据导出

## 🗄️ 数据库设计

### MySQL数据库配置
- **主机**：localhost
- **用户名**：root
- **密码**：123456
- **数据库名**：car-park

### 数据库表结构
1. **admins** - 管理员表
2. **vehicles** - 车辆信息表
3. **parking_records** - 停车记录表
4. **pricing_config** - 价格配置表
5. **payment_qrcodes** - 支付二维码表

## 🚀 系统启动

### 环境要求
- Python 3.8+
- MySQL 5.7+
- 摄像头设备（可选）

### 安装步骤

1. **安装Python依赖**：
   ```bash
   pip install -r requirements.txt
   ```

2. **启动MySQL服务**：
   确保MySQL服务已启动，用户名root，密码123456

3. **启动系统**：
   ```bash
   python run.py
   ```

4. **访问系统**：
   - 地址：http://localhost:5000
   - 管理员账户：admin
   - 管理员密码：admin123

## 📁 项目文件结构

```
写字楼停车管理系统/
├── app.py                          # 主应用文件
├── run.py                          # 启动脚本
├── license_plate_recognition.py    # 车牌识别模块
├── requirements.txt               # 依赖包列表
├── README.md                      # 项目说明
├── 系统说明.md                     # 详细说明文档
├── templates/                     # HTML模板文件
│   ├── base.html                  # 基础模板
│   ├── login.html                 # 登录页面
│   ├── dashboard.html             # 控制台
│   ├── vehicle_recognition.html   # 车辆识别
│   ├── parking_management.html    # 停车管理
│   ├── pricing_settings.html      # 价格设置
│   ├── statistics.html            # 统计报表
│   ├── payment_settings.html      # 支付设置
│   └── payment.html               # 支付页面
├── static/                        # 静态资源
│   ├── css/style.css              # 样式文件
│   ├── js/main.js                 # JavaScript文件
│   ├── uploads/                   # 上传文件目录
│   └── qrcodes/                   # 二维码存储目录
```

## 🎯 系统特色

### 技术特色
1. **现代化架构**：Flask + SQLAlchemy + MySQL
2. **响应式设计**：Bootstrap 5 + 自定义CSS
3. **安全可靠**：完整的权限控制和数据验证
4. **高性能**：数据库索引优化，分页查询

### 功能特色
1. **智能识别**：PaddleOCR高精度车牌识别
2. **灵活计费**：可配置的收费规则和会员优惠
3. **便捷支付**：集成微信/支付宝二维码支付
4. **实时统计**：丰富的数据统计和图表展示

### 用户体验
1. **界面美观**：现代化的UI设计
2. **操作简单**：直观的用户操作流程
3. **反馈及时**：完善的操作反馈机制
4. **移动适配**：支持PC和手机访问

## 🔧 系统使用指南

### 管理员登录
1. 访问 http://localhost:5000
2. 使用账户 admin / admin123 登录

### 车辆入场
1. 进入"车辆识别"页面
2. 上传车辆图片或使用摄像头拍照
3. 系统自动识别车牌号
4. 点击"确认入场"完成入场登记

### 车辆出场
1. 车辆识别页面识别车牌后点击"确认出场"
2. 系统自动计算停车费用
3. 如有费用则跳转到支付页面
4. 选择支付方式完成支付

### 价格设置
1. 进入"价格设置"页面
2. 配置每小时费用、免费时间、日最高费用
3. 管理会员车辆和折扣设置
4. 保存配置

### 支付配置
1. 进入"支付设置"页面
2. 上传微信和支付宝收款二维码图片
3. 系统会在支付时显示对应的二维码

### 统计查看
1. 控制台页面查看实时统计
2. "统计报表"页面查看详细数据和图表
3. 支持数据导出功能

## ⚠️ 注意事项

### 生产环境部署
1. **修改默认密码**：登录后立即修改管理员密码
2. **数据库安全**：生产环境建议创建专用数据库用户
3. **HTTPS配置**：生产环境建议配置SSL证书
4. **防火墙设置**：合理配置服务器防火墙规则

### 系统维护
1. **定期备份**：定期备份数据库数据
2. **日志监控**：监控系统运行日志
3. **性能优化**：根据使用情况优化数据库查询
4. **安全更新**：定期更新依赖包版本

## 🆘 常见问题

### Q: 车牌识别准确率不高？
A: 
1. 确保图片清晰，光线充足
2. 车牌在图片中占比适中
3. 避免车牌被遮挡或倾斜
4. 可以尝试多次识别

### Q: 数据库连接失败？
A: 
1. 确认MySQL服务已启动
2. 检查用户名密码是否正确
3. 确认数据库是否存在

### Q: 摄像头无法启动？
A: 
1. 检查摄像头是否被其他程序占用
2. 确认浏览器已授权摄像头权限
3. 尝试刷新页面重新启动

### Q: 支付二维码不显示？
A: 
1. 检查是否已上传对应的支付二维码
2. 确认图片格式正确（JPG/PNG）
3. 检查图片文件是否损坏

## 📞 技术支持

系统已经过完整测试，可以直接投入使用。如有技术问题，请检查：
1. 系统环境是否满足要求
2. 依赖包是否正确安装
3. 数据库配置是否正确
4. 日志文件中的错误信息

---

**开发完成时间**：2024年
**系统版本**：v1.0 生产版
**技术支持**：提供完整的部署和使用文档
