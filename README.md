# 写字楼停车管理系统

一个基于Python Flask开发的智能停车管理系统，支持车牌识别、收费管理、会员管理等功能。

## 功能特性

### 🚗 车牌识别
- **手动上传识别**：支持上传车辆图片进行车牌识别
- **实时摄像头识别**：支持调用摄像头实时识别车牌
- **智能算法**：基于PaddleOCR的高精度中文车牌识别

### 💰 收费管理
- **灵活计费**：支持按小时计费，可设置免费时间
- **日最高限额**：可设置每日最高收费金额
- **会员优惠**：支持会员折扣价格
- **多种支付**：支持微信、支付宝扫码支付

### 👥 用户管理
- **管理员登录**：安全的管理员认证系统
- **会员管理**：车辆会员信息管理和折扣设置
- **权限控制**：基于角色的访问控制

### 📊 统计报表
- **实时统计**：当前在场车辆、今日收入等实时数据
- **图表展示**：收入趋势、停车统计等可视化图表
- **数据导出**：支持Excel格式数据导出

### ⚙️ 系统管理
- **价格设置**：灵活的收费标准配置
- **支付配置**：微信、支付宝收款码管理
- **手动操作**：支持手动入场、出场操作

## 技术栈

- **后端框架**：Flask + SQLAlchemy
- **数据库**：MySQL
- **车牌识别**：OpenCV + PaddleOCR
- **前端技术**：HTML5 + CSS3 + JavaScript + Bootstrap 5
- **图表库**：Chart.js

## 快速启动

### 环境要求

- Python 3.8+
- MySQL 5.7+
- 摄像头设备（可选）

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置数据库

确保MySQL服务已启动，使用以下配置：
- 主机：localhost
- 用户名：root
- 密码：123456
- 数据库：car-park（系统会自动创建）

### 3. 启动系统

```bash
python run.py
```

### 4. 访问系统

- 访问地址：http://localhost:5000
- 管理员账户：admin / admin123

## 使用说明

### 车辆入场

1. **自动识别入场**：
   - 进入"车辆识别"页面
   - 上传车辆图片或使用摄像头拍照
   - 系统自动识别车牌号
   - 点击"确认入场"完成入场登记

2. **手动入场**：
   - 在控制台点击"手动入场"
   - 输入车牌号和车主信息
   - 选择是否为会员车辆
   - 确认入场

### 车辆出场

1. **自动识别出场**：
   - 车辆识别页面识别车牌后点击"确认出场"
   - 系统自动计算停车费用
   - 如有费用则跳转到支付页面

2. **手动出场**：
   - 控制台点击"手动出场"或在停车管理页面操作
   - 输入车牌号搜索车辆
   - 确认出场并处理支付

### 支付流程

1. 车辆出场时如产生费用，系统会显示支付页面
2. 选择支付方式（微信或支付宝）
3. 扫描显示的收款二维码完成支付
4. 点击"确认已支付"完成流程

### 价格设置

1. 进入"价格设置"页面
2. 配置每小时费用、免费时间、日最高费用
3. 管理会员车辆和折扣设置
4. 保存配置

### 支付配置

1. 进入"支付设置"页面
2. 上传微信和支付宝收款二维码图片
3. 系统会在支付时显示对应的二维码

## 目录结构

```
写字楼停车管理系统/
├── app.py                 # 主应用文件
├── license_plate_recognition.py  # 车牌识别模块
├── requirements.txt       # 依赖包列表
├── README.md             # 说明文档
├── templates/            # HTML模板
│   ├── base.html
│   ├── login.html
│   ├── dashboard.html
│   ├── vehicle_recognition.html
│   ├── parking_management.html
│   ├── pricing_settings.html
│   ├── statistics.html
│   ├── payment_settings.html
│   └── payment.html
├── static/               # 静态资源
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   ├── uploads/          # 上传的图片
│   └── qrcodes/          # 支付二维码
```

## 数据库表结构

### admins - 管理员表
- id: 主键
- username: 用户名
- password_hash: 密码哈希
- created_at: 创建时间

### vehicles - 车辆信息表
- id: 主键
- license_plate: 车牌号
- owner_name: 车主姓名
- phone: 联系电话
- is_member: 是否会员
- member_discount: 会员折扣
- created_at: 创建时间

### parking_records - 停车记录表
- id: 主键
- license_plate: 车牌号
- entry_time: 入场时间
- exit_time: 出场时间
- parking_fee: 停车费用
- is_paid: 是否已支付
- payment_method: 支付方式
- entry_method: 入场方式
- exit_method: 出场方式
- created_at: 创建时间

### pricing_config - 价格配置表
- id: 主键
- config_name: 配置名称
- hourly_rate: 每小时费用
- daily_max: 日最高费用
- free_minutes: 免费时间（分钟）
- is_active: 是否激活
- updated_at: 更新时间

### payment_qrcodes - 支付二维码表
- id: 主键
- payment_type: 支付类型（wechat/alipay）
- qr_image_path: 二维码图片路径
- is_active: 是否激活
- uploaded_at: 上传时间

## 常见问题

### Q: 车牌识别准确率不高怎么办？
A:
1. 确保图片清晰，光线充足
2. 车牌在图片中占比适中
3. 避免车牌被遮挡或倾斜
4. 可以尝试多次识别或手动输入

### Q: 摄像头无法启动？
A:
1. 检查摄像头是否被其他程序占用
2. 确认浏览器已授权摄像头权限
3. 尝试刷新页面重新启动

### Q: 支付二维码不显示？
A:
1. 检查是否已上传对应的支付二维码
2. 确认图片格式正确（JPG/PNG）
3. 检查图片文件是否损坏

### Q: 数据库连接失败？
A:
1. 确认MySQL服务已启动
2. 检查数据库连接配置是否正确
3. 确认数据库用户权限

## 更新日志

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础车牌识别功能
- 停车收费管理
- 会员管理系统
- 统计报表功能
- 支付二维码集成

## 许可证

本项目采用 MIT 许可证，详见 LICENSE 文件。

## 技术支持

如有问题或建议，请联系开发团队。
