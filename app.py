from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Login<PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
from datetime import datetime, timedelta
from license_plate_recognition import LicensePlateRecognizer
from decimal import Decimal
from sqlalchemy import DECIMAL

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:123456@localhost/car-park'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static/qrcodes', exist_ok=True)

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# 初始化车牌识别器
recognizer = LicensePlateRecognizer()

# 数据库模型
class Admin(UserMixin, db.Model):
    __tablename__ = 'admins'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)

class Vehicle(db.Model):
    __tablename__ = 'vehicles'
    id = db.Column(db.Integer, primary_key=True)
    license_plate = db.Column(db.String(20), unique=True, nullable=False)
    owner_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_member = db.Column(db.Boolean, default=False)
    member_discount = db.Column(DECIMAL(3, 2), default=1.0)  # 1.0 = no discount, 0.8 = 20% off
    created_at = db.Column(db.DateTime, default=datetime.now)

class ParkingRecord(db.Model):
    __tablename__ = 'parking_records'
    id = db.Column(db.Integer, primary_key=True)
    license_plate = db.Column(db.String(20), nullable=False)
    entry_time = db.Column(db.DateTime, nullable=False)
    exit_time = db.Column(db.DateTime)
    parking_fee = db.Column(DECIMAL(10, 2))
    is_paid = db.Column(db.Boolean, default=False)
    payment_method = db.Column(db.String(20))  # 'wechat', 'alipay', 'cash'
    entry_method = db.Column(db.String(20), default='auto')  # 'auto', 'manual'
    exit_method = db.Column(db.String(20), default='auto')
    created_at = db.Column(db.DateTime, default=datetime.now)

class PricingConfig(db.Model):
    __tablename__ = 'pricing_config'
    id = db.Column(db.Integer, primary_key=True)
    config_name = db.Column(db.String(50), nullable=False)
    hourly_rate = db.Column(DECIMAL(10, 2), nullable=False)
    daily_max = db.Column(DECIMAL(10, 2))
    free_minutes = db.Column(db.Integer, default=15)
    is_active = db.Column(db.Boolean, default=True)
    updated_at = db.Column(db.DateTime, default=datetime.now)

class PaymentQRCode(db.Model):
    __tablename__ = 'payment_qrcodes'
    id = db.Column(db.Integer, primary_key=True)
    payment_type = db.Column(db.String(20), nullable=False)  # 'wechat', 'alipay'
    qr_image_path = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    uploaded_at = db.Column(db.DateTime, default=datetime.now)

@login_manager.user_loader
def load_user(user_id):
    return Admin.query.get(int(user_id))

# 路由
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        admin = Admin.query.filter_by(username=username).first()

        if admin and check_password_hash(admin.password_hash, password):
            login_user(admin)
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # 获取今日统计数据
    today = datetime.now().date()
    today_records = ParkingRecord.query.filter(
        db.func.date(ParkingRecord.created_at) == today
    ).all()

    stats = {
        'today_entries': len([r for r in today_records if r.entry_time]),
        'today_exits': len([r for r in today_records if r.exit_time]),
        'current_parked': ParkingRecord.query.filter_by(exit_time=None).count(),
        'today_revenue': sum([r.parking_fee or 0 for r in today_records if r.is_paid])
    }

    return render_template('dashboard.html', stats=stats)

@app.route('/vehicle_recognition')
@login_required
def vehicle_recognition():
    return render_template('vehicle_recognition.html')

@app.route('/parking_management')
@login_required
def parking_management():
    # 获取当前在场车辆
    current_vehicles = ParkingRecord.query.filter_by(exit_time=None).all()
    return render_template('parking_management.html', vehicles=current_vehicles)

@app.route('/pricing_settings')
@login_required
def pricing_settings():
    pricing = PricingConfig.query.filter_by(is_active=True).first()
    return render_template('pricing_settings.html', pricing=pricing)

@app.route('/statistics')
@login_required
def statistics():
    return render_template('statistics.html')

@app.route('/payment_settings')
@login_required
def payment_settings():
    qrcodes = PaymentQRCode.query.filter_by(is_active=True).all()
    return render_template('payment_settings.html', qrcodes=qrcodes)

@app.route('/payment/<int:record_id>')
def payment_page(record_id):
    record = ParkingRecord.query.get_or_404(record_id)
    if record.is_paid:
        flash('该订单已支付', 'info')
        return redirect(url_for('dashboard'))

    qrcodes = PaymentQRCode.query.filter_by(is_active=True).all()
    return render_template('payment.html', record=record, qrcodes=qrcodes)

# API路由
@app.route('/api/upload_image', methods=['POST'])
@login_required
def upload_image():
    if 'image' not in request.files:
        return jsonify({'success': False, 'message': '没有上传文件'})

    file = request.files['image']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})

    if file:
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        # 识别车牌
        license_plate, message = recognizer.recognize_from_image(filepath)

        if license_plate:
            return jsonify({
                'success': True,
                'license_plate': license_plate,
                'message': message,
                'image_path': filepath
            })
        else:
            return jsonify({'success': False, 'message': message})

@app.route('/api/camera_capture', methods=['POST'])
@login_required
def camera_capture():
    try:
        license_plate, message = recognizer.recognize_from_camera()

        if license_plate:
            return jsonify({
                'success': True,
                'license_plate': license_plate,
                'message': message
            })
        else:
            return jsonify({'success': False, 'message': message})
    except Exception as e:
        return jsonify({'success': False, 'message': f'摄像头错误: {str(e)}'})

@app.route('/api/manual_entry', methods=['POST'])
@login_required
def manual_entry():
    data = request.get_json()
    license_plate = data.get('license_plate', '').upper()

    if not license_plate:
        return jsonify({'success': False, 'message': '车牌号不能为空'})

    # 检查是否已经在场
    existing = ParkingRecord.query.filter_by(
        license_plate=license_plate,
        exit_time=None
    ).first()

    if existing:
        return jsonify({'success': False, 'message': '该车辆已在场内'})

    # 创建或更新车辆信息
    vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
    if not vehicle:
        vehicle = Vehicle(
            license_plate=license_plate,
            owner_name=data.get('owner_name', ''),
            phone=data.get('owner_phone', ''),
            is_member=data.get('is_member', False)
        )
        db.session.add(vehicle)
    else:
        if data.get('owner_name'):
            vehicle.owner_name = data.get('owner_name')
        if data.get('owner_phone'):
            vehicle.phone = data.get('owner_phone')
        vehicle.is_member = data.get('is_member', False)

    # 创建停车记录
    record = ParkingRecord(
        license_plate=license_plate,
        entry_time=datetime.now(),
        entry_method='manual'
    )

    db.session.add(record)
    db.session.commit()

    return jsonify({'success': True, 'message': '入场成功'})

def calculate_parking_fee(entry_time, exit_time, is_member=False, license_plate=None):
    """计算停车费用"""
    pricing = PricingConfig.query.filter_by(is_active=True).first()
    if not pricing:
        return 0

    # 计算停车时长（分钟）
    duration = (exit_time - entry_time).total_seconds() / 60

    # 免费时间
    if duration <= pricing.free_minutes:
        return 0

    # 计算收费时长（小时，向上取整）
    billable_hours = max(1, int((duration - pricing.free_minutes) / 60) +
                        (1 if (duration - pricing.free_minutes) % 60 > 0 else 0))

    # 基础费用
    fee = billable_hours * pricing.hourly_rate

    # 日最高限额
    if pricing.daily_max and fee > pricing.daily_max:
        fee = pricing.daily_max

    # 会员折扣
    if is_member and license_plate:
        vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
        if vehicle and vehicle.member_discount:
            fee = fee * vehicle.member_discount

    return float(fee)

@app.route('/api/manual_exit', methods=['POST'])
@login_required
def manual_exit():
    data = request.get_json()
    license_plate = data.get('license_plate', '').upper()

    if not license_plate:
        return jsonify({'success': False, 'message': '车牌号不能为空'})

    # 查找停车记录
    record = ParkingRecord.query.filter_by(
        license_plate=license_plate,
        exit_time=None
    ).first()

    if not record:
        return jsonify({'success': False, 'message': '未找到该车辆的停车记录'})

    # 更新出场时间
    exit_time = datetime.now()
    record.exit_time = exit_time
    record.exit_method = 'manual'

    # 计算停车费用
    vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
    is_member = vehicle.is_member if vehicle else False

    fee = calculate_parking_fee(record.entry_time, exit_time, is_member, license_plate)
    record.parking_fee = Decimal(str(fee))

    db.session.commit()

    return jsonify({
        'success': True,
        'message': '出场成功',
        'fee': fee,
        'record_id': record.id
    })

@app.route('/api/search_parked_vehicle/<license_plate>')
@login_required
def search_parked_vehicle(license_plate):
    license_plate = license_plate.upper()

    record = ParkingRecord.query.filter_by(
        license_plate=license_plate,
        exit_time=None
    ).first()

    if not record:
        return jsonify({'found': False})

    # 计算停车时长
    now = datetime.now()
    duration = now - record.entry_time
    hours = int(duration.total_seconds() // 3600)
    minutes = int((duration.total_seconds() % 3600) // 60)

    # 计算预计费用
    vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
    is_member = vehicle.is_member if vehicle else False
    estimated_fee = calculate_parking_fee(record.entry_time, now, is_member, license_plate)

    return jsonify({
        'found': True,
        'entry_time': record.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
        'duration': f'{hours}小时{minutes}分钟',
        'estimated_fee': f'{estimated_fee:.2f}'
    })

@app.route('/api/recent_records')
@login_required
def recent_records():
    records = ParkingRecord.query.order_by(ParkingRecord.created_at.desc()).limit(10).all()

    result = []
    for record in records:
        result.append({
            'license_plate': record.license_plate,
            'entry_time': record.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
            'exit_time': record.exit_time.strftime('%Y-%m-%d %H:%M:%S') if record.exit_time else None,
            'parking_fee': f'{record.parking_fee:.2f}' if record.parking_fee else '0.00',
            'is_paid': record.is_paid
        })

    return jsonify({'records': result})

@app.route('/api/update_pricing', methods=['POST'])
@login_required
def update_pricing():
    data = request.get_json()

    # 停用当前配置
    current_pricing = PricingConfig.query.filter_by(is_active=True).first()
    if current_pricing:
        current_pricing.is_active = False

    # 创建新配置
    new_pricing = PricingConfig(
        config_name=data.get('config_name', '默认配置'),
        hourly_rate=Decimal(str(data.get('hourly_rate', 5))),
        daily_max=Decimal(str(data.get('daily_max', 50))) if data.get('daily_max') else None,
        free_minutes=int(data.get('free_minutes', 15)),
        is_active=True
    )

    db.session.add(new_pricing)
    db.session.commit()

    return jsonify({'success': True, 'message': '价格设置已更新'})

@app.route('/api/upload_qrcode', methods=['POST'])
@login_required
def upload_qrcode():
    if 'qrcode' not in request.files:
        return jsonify({'success': False, 'message': '没有上传文件'})

    file = request.files['qrcode']
    payment_type = request.form.get('payment_type')

    if file.filename == '' or not payment_type:
        return jsonify({'success': False, 'message': '请选择文件和支付类型'})

    if file:
        filename = secure_filename(f"{payment_type}_{file.filename}")
        filepath = os.path.join('static/qrcodes', filename)
        file.save(filepath)

        # 停用旧的二维码
        old_qrcode = PaymentQRCode.query.filter_by(
            payment_type=payment_type,
            is_active=True
        ).first()
        if old_qrcode:
            old_qrcode.is_active = False

        # 创建新的二维码记录
        new_qrcode = PaymentQRCode(
            payment_type=payment_type,
            qr_image_path=filepath,
            is_active=True
        )

        db.session.add(new_qrcode)
        db.session.commit()

        return jsonify({'success': True, 'message': '二维码上传成功'})

@app.route('/api/add_member', methods=['POST'])
@login_required
def add_member():
    data = request.get_json()
    license_plate = data.get('license_plate', '').upper()

    if not license_plate:
        return jsonify({'success': False, 'message': '车牌号不能为空'})

    # 检查车辆是否已存在
    vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()

    if vehicle:
        # 更新现有车辆信息
        vehicle.owner_name = data.get('owner_name', '')
        vehicle.phone = data.get('phone', '')
        vehicle.is_member = True
        vehicle.member_discount = Decimal(str(data.get('member_discount', 0.9)))
    else:
        # 创建新车辆
        vehicle = Vehicle(
            license_plate=license_plate,
            owner_name=data.get('owner_name', ''),
            phone=data.get('phone', ''),
            is_member=True,
            member_discount=Decimal(str(data.get('member_discount', 0.9)))
        )
        db.session.add(vehicle)

    db.session.commit()
    return jsonify({'success': True, 'message': '会员添加成功'})

@app.route('/api/members')
@login_required
def get_members():
    members = Vehicle.query.filter_by(is_member=True).all()

    result = []
    for member in members:
        result.append({
            'license_plate': member.license_plate,
            'owner_name': member.owner_name,
            'phone': member.phone,
            'member_discount': float(member.member_discount)
        })

    return jsonify({'members': result})

@app.route('/api/remove_member', methods=['POST'])
@login_required
def remove_member():
    data = request.get_json()
    license_plate = data.get('license_plate', '').upper()

    vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
    if vehicle:
        vehicle.is_member = False
        vehicle.member_discount = Decimal('1.0')
        db.session.commit()
        return jsonify({'success': True, 'message': '会员删除成功'})
    else:
        return jsonify({'success': False, 'message': '未找到该车辆'})

@app.route('/api/confirm_payment', methods=['POST'])
def confirm_payment():
    data = request.get_json()
    record_id = data.get('record_id')
    payment_method = data.get('payment_method')

    record = ParkingRecord.query.get(record_id)
    if not record:
        return jsonify({'success': False, 'message': '未找到停车记录'})

    if record.is_paid:
        return jsonify({'success': False, 'message': '该订单已支付'})

    record.is_paid = True
    record.payment_method = payment_method
    db.session.commit()

    return jsonify({'success': True, 'message': '支付确认成功'})

@app.route('/api/statistics')
@login_required
def get_statistics():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    report_type = request.args.get('report_type', 'daily')

    # 构建查询条件
    query = ParkingRecord.query
    if start_date:
        query = query.filter(ParkingRecord.created_at >= start_date)
    if end_date:
        query = query.filter(ParkingRecord.created_at <= end_date + ' 23:59:59')

    records = query.all()

    # 计算概览数据
    total_entries = len([r for r in records if r.entry_time])
    total_exits = len([r for r in records if r.exit_time])
    total_revenue = sum([float(r.parking_fee or 0) for r in records if r.is_paid])

    # 计算平均停车时长
    completed_records = [r for r in records if r.entry_time and r.exit_time]
    if completed_records:
        total_duration = sum([(r.exit_time - r.entry_time).total_seconds() for r in completed_records])
        avg_duration_hours = total_duration / len(completed_records) / 3600
        avg_duration = f"{int(avg_duration_hours)}小时{int((avg_duration_hours % 1) * 60)}分钟"
    else:
        avg_duration = "0小时"

    # 生成图表数据
    charts = generate_chart_data(records, report_type)

    return jsonify({
        'overview': {
            'total_entries': total_entries,
            'total_exits': total_exits,
            'total_revenue': total_revenue,
            'avg_duration': avg_duration
        },
        'charts': charts
    })

def generate_chart_data(records, report_type=None):
    from collections import defaultdict

    # 按日期分组
    daily_data = defaultdict(lambda: {'entries': 0, 'exits': 0, 'revenue': 0})
    payment_data = defaultdict(lambda: {'count': 0, 'amount': 0})
    member_data = {'member': 0, 'normal': 0}

    for record in records:
        date_key = record.created_at.strftime('%Y-%m-%d')

        if record.entry_time:
            daily_data[date_key]['entries'] += 1
        if record.exit_time:
            daily_data[date_key]['exits'] += 1
        if record.is_paid and record.parking_fee:
            daily_data[date_key]['revenue'] += float(record.parking_fee)

            # 支付方式统计
            method = record.payment_method or '现金'
            payment_data[method]['count'] += 1
            payment_data[method]['amount'] += float(record.parking_fee)

        # 会员统计
        vehicle = Vehicle.query.filter_by(license_plate=record.license_plate).first()
        if vehicle and vehicle.is_member:
            member_data['member'] += 1
        else:
            member_data['normal'] += 1

    # 生成图表数据
    sorted_dates = sorted(daily_data.keys())

    return {
        'revenue': {
            'labels': sorted_dates,
            'data': [daily_data[date]['revenue'] for date in sorted_dates]
        },
        'parking': {
            'labels': sorted_dates,
            'data': [daily_data[date]['entries'] for date in sorted_dates]
        },
        'payment': {
            'labels': list(payment_data.keys()),
            'data': [payment_data[method]['amount'] for method in payment_data.keys()]
        },
        'member': {
            'data': [member_data['member'], member_data['normal']]
        }
    }

@app.route('/api/detail_records')
@login_required
def get_detail_records():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))

    # 构建查询
    query = ParkingRecord.query
    if start_date:
        query = query.filter(ParkingRecord.created_at >= start_date)
    if end_date:
        query = query.filter(ParkingRecord.created_at <= end_date + ' 23:59:59')

    # 分页
    pagination = query.order_by(ParkingRecord.created_at.desc()).paginate(
        page=page, per_page=page_size, error_out=False
    )

    records = []
    for record in pagination.items:
        # 计算停车时长
        duration = None
        if record.entry_time and record.exit_time:
            delta = record.exit_time - record.entry_time
            hours = int(delta.total_seconds() // 3600)
            minutes = int((delta.total_seconds() % 3600) // 60)
            duration = f"{hours}小时{minutes}分钟"

        # 检查是否为会员
        vehicle = Vehicle.query.filter_by(license_plate=record.license_plate).first()
        is_member = vehicle.is_member if vehicle else False

        records.append({
            'date': record.created_at.strftime('%Y-%m-%d'),
            'license_plate': record.license_plate,
            'entry_time': record.entry_time.strftime('%H:%M:%S') if record.entry_time else '-',
            'exit_time': record.exit_time.strftime('%H:%M:%S') if record.exit_time else '-',
            'duration': duration,
            'parking_fee': f'{record.parking_fee:.2f}' if record.parking_fee else '0.00',
            'payment_method': record.payment_method or '未支付',
            'is_member': is_member
        })

    return jsonify({
        'records': records,
        'total_pages': pagination.pages
    })

@app.route('/api/payment_stats')
@login_required
def get_payment_stats():
    # 获取最近30天的支付数据
    from datetime import datetime, timedelta
    thirty_days_ago = datetime.now() - timedelta(days=30)

    records = ParkingRecord.query.filter(
        ParkingRecord.is_paid == True,
        ParkingRecord.created_at >= thirty_days_ago
    ).all()

    payment_stats = {}
    for record in records:
        method = record.payment_method or '现金'
        if method not in payment_stats:
            payment_stats[method] = {'count': 0, 'amount': 0}

        payment_stats[method]['count'] += 1
        payment_stats[method]['amount'] += float(record.parking_fee or 0)

    # 转换为图表数据格式
    labels = list(payment_stats.keys())
    amounts = [payment_stats[method]['amount'] for method in labels]
    details = [
        {
            'method': method,
            'count': stats['count'],
            'amount': stats['amount']
        }
        for method, stats in payment_stats.items()
    ]

    return jsonify({
        'labels': labels,
        'amounts': amounts,
        'details': details
    })

@app.route('/api/parked_vehicles')
@login_required
def get_parked_vehicles():
    page = int(request.args.get('page', 1))
    page_size = int(request.args.get('page_size', 20))
    search = request.args.get('search', '')
    filter_type = request.args.get('filter_type', '')
    sort_by = request.args.get('sort_by', 'entry_time')

    # 基础查询
    query = ParkingRecord.query.filter_by(exit_time=None)

    # 搜索过滤
    if search:
        query = query.filter(ParkingRecord.license_plate.like(f'%{search}%'))

    # 类型过滤
    if filter_type == 'member':
        member_plates = [v.license_plate for v in Vehicle.query.filter_by(is_member=True).all()]
        query = query.filter(ParkingRecord.license_plate.in_(member_plates))
    elif filter_type == 'normal':
        member_plates = [v.license_plate for v in Vehicle.query.filter_by(is_member=True).all()]
        query = query.filter(~ParkingRecord.license_plate.in_(member_plates))

    # 排序
    if sort_by == 'entry_time':
        query = query.order_by(ParkingRecord.entry_time.desc())
    elif sort_by == 'duration':
        query = query.order_by(ParkingRecord.entry_time.asc())

    # 分页
    pagination = query.paginate(page=page, per_page=page_size, error_out=False)

    vehicles = []
    member_count = 0
    long_parked = 0
    estimated_revenue = 0

    for record in pagination.items:
        # 获取车辆信息
        vehicle = Vehicle.query.filter_by(license_plate=record.license_plate).first()
        is_member = vehicle.is_member if vehicle else False

        # 计算停车时长
        now = datetime.now()
        duration = now - record.entry_time
        hours = int(duration.total_seconds() // 3600)
        minutes = int((duration.total_seconds() % 3600) // 60)
        duration_str = f"{hours}小时{minutes}分钟"

        # 计算预计费用
        estimated_fee = calculate_parking_fee(record.entry_time, now, is_member, record.license_plate)

        # 统计数据
        if is_member:
            member_count += 1
        if hours >= 24:  # 超过24小时算超时
            long_parked += 1
        estimated_revenue += estimated_fee

        vehicles.append({
            'license_plate': record.license_plate,
            'owner_name': vehicle.owner_name if vehicle else None,
            'entry_time': record.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration': duration_str,
            'estimated_fee': f'{estimated_fee:.2f}',
            'is_member': is_member
        })

    # 统计信息
    statistics = {
        'total_parked': pagination.total,
        'member_count': member_count,
        'long_parked': long_parked,
        'estimated_revenue': estimated_revenue
    }

    return jsonify({
        'vehicles': vehicles,
        'total_pages': pagination.pages,
        'statistics': statistics
    })

@app.route('/api/vehicle_detail/<license_plate>')
@login_required
def get_vehicle_detail(license_plate):
    license_plate = license_plate.upper()

    # 获取车辆信息
    vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()

    # 获取当前停车记录
    current_record = ParkingRecord.query.filter_by(
        license_plate=license_plate,
        exit_time=None
    ).first()

    if not current_record:
        return jsonify({'success': False, 'message': '未找到该车辆的停车记录'})

    # 计算停车信息
    now = datetime.now()
    duration = now - current_record.entry_time
    hours = int(duration.total_seconds() // 3600)
    minutes = int((duration.total_seconds() % 3600) // 60)
    duration_str = f"{hours}小时{minutes}分钟"

    is_member = vehicle.is_member if vehicle else False
    estimated_fee = calculate_parking_fee(current_record.entry_time, now, is_member, license_plate)

    # 获取历史记录
    history_records = ParkingRecord.query.filter_by(
        license_plate=license_plate
    ).filter(
        ParkingRecord.exit_time.isnot(None)
    ).order_by(ParkingRecord.created_at.desc()).limit(10).all()

    history = []
    for record in history_records:
        history.append({
            'date': record.created_at.strftime('%Y-%m-%d'),
            'entry_time': record.entry_time.strftime('%H:%M:%S'),
            'exit_time': record.exit_time.strftime('%H:%M:%S') if record.exit_time else None,
            'parking_fee': f'{record.parking_fee:.2f}' if record.parking_fee else '0.00',
            'is_paid': record.is_paid
        })

    return jsonify({
        'success': True,
        'vehicle': {
            'license_plate': license_plate,
            'owner_name': vehicle.owner_name if vehicle else None,
            'phone': vehicle.phone if vehicle else None,
            'is_member': is_member
        },
        'parking': {
            'entry_time': current_record.entry_time.strftime('%Y-%m-%d %H:%M:%S'),
            'duration': duration_str,
            'estimated_fee': f'{estimated_fee:.2f}',
            'entry_method': current_record.entry_method
        },
        'history': history
    })

def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    import pymysql
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )

        cursor = connection.cursor()

        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE 'car-park'")
        result = cursor.fetchone()

        if not result:
            # 创建数据库
            cursor.execute("CREATE DATABASE `car-park` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("数据库 'car-park' 创建成功!")
        else:
            print("数据库 'car-park' 已存在")

        cursor.close()
        connection.close()
        return True

    except Exception as e:
        print(f"数据库操作失败: {e}")
        print("请确保:")
        print("1. MySQL服务已启动")
        print("2. 用户名密码正确 (root/123456)")
        print("3. 已安装PyMySQL: pip install pymysql")
        return False

if __name__ == '__main__':
    print("=" * 50)
    print("写字楼停车管理系统")
    print("=" * 50)

    # 创建数据库
    if not create_database_if_not_exists():
        print("数据库初始化失败，程序退出")
        exit(1)

    try:
        with app.app_context():
            # 创建表
            db.create_all()
            print("数据库表创建成功!")

            # 创建默认管理员账户
            if not Admin.query.filter_by(username='admin').first():
                admin = Admin(
                    username='admin',
                    password_hash=generate_password_hash('admin123')
                )
                db.session.add(admin)
                print("默认管理员账户已创建: admin/admin123")

            # 创建默认价格配置
            if not PricingConfig.query.filter_by(is_active=True).first():
                default_pricing = PricingConfig(
                    config_name='默认价格配置',
                    hourly_rate=Decimal('5.00'),
                    daily_max=Decimal('50.00'),
                    free_minutes=15,
                    is_active=True
                )
                db.session.add(default_pricing)
                print("默认价格配置已创建: 5元/小时，日最高50元，免费15分钟")

            db.session.commit()
            print("数据初始化完成!")

        print("\n" + "=" * 50)
        print("系统启动成功!")
        print("访问地址: http://localhost:5000")
        print("管理员账户: admin / admin123")
        print("按 Ctrl+C 停止服务")
        print("=" * 50)

        app.run(debug=True, host='0.0.0.0', port=5000)

    except Exception as e:
        print(f"系统启动失败: {e}")
        print("请检查数据库连接和配置")
