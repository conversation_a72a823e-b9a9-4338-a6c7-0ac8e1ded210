from flask import Flask, request, jsonify
import os
from werkzeug.utils import secure_filename

app = Flask(__name__)
app.config['SECRET_KEY'] = 'debug-key'
app.config['UPLOAD_FOLDER'] = 'static/uploads'

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

@app.route('/')
def index():
    return "Debug Flask App is running!"

@app.route('/api/upload_image', methods=['POST'])
def upload_image():
    print("🔍 收到图片上传请求")
    
    if 'image' not in request.files:
        print("❌ 没有上传文件")
        return jsonify({'success': False, 'message': '没有上传文件'})

    file = request.files['image']
    if file.filename == '':
        print("❌ 没有选择文件")
        return jsonify({'success': False, 'message': '没有选择文件'})

    if file:
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        print(f"✅ 文件保存成功: {filepath}")

        # 尝试车牌识别
        try:
            from license_plate_recognition import LicensePlateRecognizer
            recognizer = LicensePlateRecognizer()
            license_plate, message = recognizer.recognize_from_image(filepath)
            
            print(f"🎯 识别结果: {license_plate}")
            print(f"📝 消息: {message}")

            if license_plate:
                return jsonify({
                    'success': True,
                    'license_plate': license_plate,
                    'message': message,
                    'image_path': filepath
                })
            else:
                return jsonify({'success': False, 'message': message})
                
        except Exception as e:
            print(f"❌ 识别过程出错: {e}")
            return jsonify({'success': False, 'message': f'识别过程中出错: {str(e)}'})

@app.route('/api/camera_capture', methods=['POST'])
def camera_capture():
    print("📷 收到摄像头拍照请求")
    
    try:
        from license_plate_recognition import LicensePlateRecognizer
        recognizer = LicensePlateRecognizer()
        license_plate, message = recognizer.recognize_from_camera()
        
        print(f"🎯 摄像头识别结果: {license_plate}")
        print(f"📝 消息: {message}")

        if license_plate:
            return jsonify({
                'success': True,
                'license_plate': license_plate,
                'message': message
            })
        else:
            return jsonify({'success': False, 'message': message})
    except Exception as e:
        print(f"❌ 摄像头识别出错: {e}")
        return jsonify({'success': False, 'message': f'摄像头错误: {str(e)}'})

if __name__ == '__main__':
    print("🚀 启动调试Flask应用...")
    print("📱 访问地址: http://localhost:5001")
    app.run(debug=True, host='127.0.0.1', port=5001)
