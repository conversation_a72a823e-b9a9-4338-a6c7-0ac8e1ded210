#!/usr/bin/env python3
"""
快速测试Flask启动
"""
import sys
import os

print("Python版本:", sys.version)
print("当前目录:", os.getcwd())

try:
    from flask import Flask
    print("✅ Flask导入成功")
    
    app = Flask(__name__)
    
    @app.route('/')
    def hello():
        return "Hello World!"
    
    print("✅ Flask应用创建成功")
    print("启动服务器...")
    
    app.run(debug=True, host='127.0.0.1', port=5004)
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
