{% extends "base.html" %}

{% block title %}价格设置 - 写字楼停车管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-dollar-sign"></i> 价格设置</h2>
        <hr>
    </div>
</div>

<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-cog"></i> 停车收费配置</h5>
            </div>
            <div class="card-body">
                <form id="pricingForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="configName" class="form-label">配置名称</label>
                                <input type="text" class="form-control" id="configName" 
                                       value="{% if pricing %}{{ pricing.config_name }}{% else %}默认配置{% endif %}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="hourlyRate" class="form-label">每小时费用 (元)</label>
                                <input type="number" class="form-control" id="hourlyRate" step="0.01" min="0"
                                       value="{% if pricing %}{{ pricing.hourly_rate }}{% else %}5.00{% endif %}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="dailyMax" class="form-label">日最高费用 (元，可选)</label>
                                <input type="number" class="form-control" id="dailyMax" step="0.01" min="0"
                                       value="{% if pricing and pricing.daily_max %}{{ pricing.daily_max }}{% endif %}">
                                <div class="form-text">留空表示不限制日最高费用</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="freeMinutes" class="form-label">免费时间 (分钟)</label>
                                <input type="number" class="form-control" id="freeMinutes" min="0"
                                       value="{% if pricing %}{{ pricing.free_minutes }}{% else %}15{% endif %}" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> 保存设置
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i> 重置
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 会员管理 -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="fas fa-users"></i> 会员管理</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <button class="btn btn-success" onclick="showAddMemberModal()">
                        <i class="fas fa-plus"></i> 添加会员
                    </button>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-striped" id="membersTable">
                        <thead>
                            <tr>
                                <th>车牌号</th>
                                <th>车主姓名</th>
                                <th>联系电话</th>
                                <th>会员折扣</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- 收费说明 -->
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 收费说明</h5>
            </div>
            <div class="card-body">
                <div id="pricingPreview">
                    <p><strong>当前收费标准：</strong></p>
                    <ul>
                        <li>免费时间：<span id="previewFreeMinutes">{% if pricing %}{{ pricing.free_minutes }}{% else %}15{% endif %}</span> 分钟</li>
                        <li>每小时费用：¥<span id="previewHourlyRate">{% if pricing %}{{ pricing.hourly_rate }}{% else %}5.00{% endif %}</span></li>
                        <li>日最高费用：
                            {% if pricing and pricing.daily_max %}
                                ¥<span id="previewDailyMax">{{ pricing.daily_max }}</span>
                            {% else %}
                                <span id="previewDailyMax">不限制</span>
                            {% endif %}
                        </li>
                    </ul>
                    
                    <hr>
                    
                    <p><strong>计费示例：</strong></p>
                    <div id="exampleCalculation">
                        <small class="text-muted">
                            停车2小时30分钟：<br>
                            免费15分钟 + 计费2.5小时 = ¥<span id="exampleFee">12.50</span>
                        </small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 历史配置 -->
        <div class="card mt-3">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> 历史配置</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>配置名称</th>
                                <th>更新时间</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody id="historyTable">
                            <!-- 数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加会员模态框 -->
<div class="modal fade" id="addMemberModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加会员</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addMemberForm">
                    <div class="mb-3">
                        <label for="memberLicensePlate" class="form-label">车牌号</label>
                        <input type="text" class="form-control" id="memberLicensePlate" required>
                    </div>
                    <div class="mb-3">
                        <label for="memberOwnerName" class="form-label">车主姓名</label>
                        <input type="text" class="form-control" id="memberOwnerName" required>
                    </div>
                    <div class="mb-3">
                        <label for="memberPhone" class="form-label">联系电话</label>
                        <input type="text" class="form-control" id="memberPhone" required>
                    </div>
                    <div class="mb-3">
                        <label for="memberDiscount" class="form-label">会员折扣</label>
                        <select class="form-control" id="memberDiscount" required>
                            <option value="0.9">9折 (10% off)</option>
                            <option value="0.8">8折 (20% off)</option>
                            <option value="0.7">7折 (30% off)</option>
                            <option value="0.5">5折 (50% off)</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="addMember()">添加会员</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// 价格设置表单提交
document.getElementById('pricingForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = {
        config_name: document.getElementById('configName').value,
        hourly_rate: parseFloat(document.getElementById('hourlyRate').value),
        daily_max: document.getElementById('dailyMax').value ? parseFloat(document.getElementById('dailyMax').value) : null,
        free_minutes: parseInt(document.getElementById('freeMinutes').value)
    };
    
    fetch('/api/update_pricing', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('价格设置已更新！');
            updatePreview();
        } else {
            alert('更新失败：' + data.message);
        }
    });
});

// 更新预览
function updatePreview() {
    const freeMinutes = document.getElementById('freeMinutes').value;
    const hourlyRate = parseFloat(document.getElementById('hourlyRate').value);
    const dailyMax = document.getElementById('dailyMax').value;
    
    document.getElementById('previewFreeMinutes').textContent = freeMinutes;
    document.getElementById('previewHourlyRate').textContent = hourlyRate.toFixed(2);
    document.getElementById('previewDailyMax').textContent = dailyMax ? '¥' + parseFloat(dailyMax).toFixed(2) : '不限制';
    
    // 计算示例费用 (2.5小时)
    const exampleHours = 2.5;
    const exampleFee = exampleHours * hourlyRate;
    document.getElementById('exampleFee').textContent = exampleFee.toFixed(2);
}

// 重置表单
function resetForm() {
    document.getElementById('pricingForm').reset();
    updatePreview();
}

// 显示添加会员模态框
function showAddMemberModal() {
    $('#addMemberModal').modal('show');
}

// 添加会员
function addMember() {
    const formData = {
        license_plate: document.getElementById('memberLicensePlate').value.toUpperCase(),
        owner_name: document.getElementById('memberOwnerName').value,
        phone: document.getElementById('memberPhone').value,
        is_member: true,
        member_discount: parseFloat(document.getElementById('memberDiscount').value)
    };
    
    fetch('/api/add_member', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('会员添加成功！');
            $('#addMemberModal').modal('hide');
            loadMembers();
        } else {
            alert('添加失败：' + data.message);
        }
    });
}

// 加载会员列表
function loadMembers() {
    fetch('/api/members')
    .then(response => response.json())
    .then(data => {
        const tbody = document.querySelector('#membersTable tbody');
        tbody.innerHTML = '';
        
        data.members.forEach(member => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td>${member.license_plate}</td>
                <td>${member.owner_name || '未设置'}</td>
                <td>${member.phone || '未设置'}</td>
                <td>${(member.member_discount * 10).toFixed(1)}折</td>
                <td>
                    <button class="btn btn-sm btn-danger" onclick="removeMember('${member.license_plate}')">
                        <i class="fas fa-trash"></i> 删除
                    </button>
                </td>
            `;
        });
    });
}

// 删除会员
function removeMember(licensePlate) {
    if (confirm('确定要删除该会员吗？')) {
        fetch('/api/remove_member', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({license_plate: licensePlate})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('会员删除成功！');
                loadMembers();
            } else {
                alert('删除失败：' + data.message);
            }
        });
    }
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    updatePreview();
    loadMembers();
    
    // 监听输入变化，实时更新预览
    ['hourlyRate', 'dailyMax', 'freeMinutes'].forEach(id => {
        document.getElementById(id).addEventListener('input', updatePreview);
    });
});
</script>
{% endblock %}
