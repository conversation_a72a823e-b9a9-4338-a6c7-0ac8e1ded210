#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
写字楼停车管理系统 - SQLite版本
用于演示和测试，不需要MySQL数据库
"""

from flask import Flask, render_template, request, redirect, url_for, flash, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
import cv2
import numpy as np
from datetime import datetime, timedelta
import json
from license_plate_recognition import LicensePlateRecognizer
from decimal import Decimal

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///parking_system.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 确保上传目录存在
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs('static/qrcodes', exist_ok=True)

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# 初始化车牌识别器
recognizer = LicensePlateRecognizer()

# 数据库模型
class Admin(UserMixin, db.Model):
    __tablename__ = 'admins'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)

class Vehicle(db.Model):
    __tablename__ = 'vehicles'
    id = db.Column(db.Integer, primary_key=True)
    license_plate = db.Column(db.String(20), unique=True, nullable=False)
    owner_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_member = db.Column(db.Boolean, default=False)
    member_discount = db.Column(db.Float, default=1.0)  # 1.0 = no discount, 0.8 = 20% off
    created_at = db.Column(db.DateTime, default=datetime.now)

class ParkingRecord(db.Model):
    __tablename__ = 'parking_records'
    id = db.Column(db.Integer, primary_key=True)
    license_plate = db.Column(db.String(20), nullable=False)
    entry_time = db.Column(db.DateTime, nullable=False)
    exit_time = db.Column(db.DateTime)
    parking_fee = db.Column(db.Float)
    is_paid = db.Column(db.Boolean, default=False)
    payment_method = db.Column(db.String(20))  # 'wechat', 'alipay', 'cash'
    entry_method = db.Column(db.String(20), default='auto')  # 'auto', 'manual'
    exit_method = db.Column(db.String(20), default='auto')
    created_at = db.Column(db.DateTime, default=datetime.now)

class PricingConfig(db.Model):
    __tablename__ = 'pricing_config'
    id = db.Column(db.Integer, primary_key=True)
    config_name = db.Column(db.String(50), nullable=False)
    hourly_rate = db.Column(db.Float, nullable=False)
    daily_max = db.Column(db.Float)
    free_minutes = db.Column(db.Integer, default=15)
    is_active = db.Column(db.Boolean, default=True)
    updated_at = db.Column(db.DateTime, default=datetime.now)

class PaymentQRCode(db.Model):
    __tablename__ = 'payment_qrcodes'
    id = db.Column(db.Integer, primary_key=True)
    payment_type = db.Column(db.String(20), nullable=False)  # 'wechat', 'alipay'
    qr_image_path = db.Column(db.String(255), nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    uploaded_at = db.Column(db.DateTime, default=datetime.now)

@login_manager.user_loader
def load_user(user_id):
    return Admin.query.get(int(user_id))

# 路由
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        admin = Admin.query.filter_by(username=username).first()
        
        if admin and check_password_hash(admin.password_hash, password):
            login_user(admin)
            return redirect(url_for('dashboard'))
        else:
            flash('用户名或密码错误', 'error')
    
    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # 获取今日统计数据
    today = datetime.now().date()
    today_records = ParkingRecord.query.filter(
        db.func.date(ParkingRecord.created_at) == today
    ).all()
    
    stats = {
        'today_entries': len([r for r in today_records if r.entry_time]),
        'today_exits': len([r for r in today_records if r.exit_time]),
        'current_parked': ParkingRecord.query.filter_by(exit_time=None).count(),
        'today_revenue': sum([r.parking_fee or 0 for r in today_records if r.is_paid])
    }
    
    return render_template('dashboard.html', stats=stats)

@app.route('/vehicle_recognition')
@login_required
def vehicle_recognition():
    return render_template('vehicle_recognition.html')

@app.route('/parking_management')
@login_required
def parking_management():
    # 获取当前在场车辆
    current_vehicles = ParkingRecord.query.filter_by(exit_time=None).all()
    return render_template('parking_management.html', vehicles=current_vehicles)

@app.route('/pricing_settings')
@login_required
def pricing_settings():
    pricing = PricingConfig.query.filter_by(is_active=True).first()
    return render_template('pricing_settings.html', pricing=pricing)

@app.route('/statistics')
@login_required
def statistics():
    return render_template('statistics.html')

@app.route('/payment_settings')
@login_required
def payment_settings():
    qrcodes = PaymentQRCode.query.filter_by(is_active=True).all()
    return render_template('payment_settings.html', qrcodes=qrcodes)

@app.route('/payment/<int:record_id>')
def payment_page(record_id):
    record = ParkingRecord.query.get_or_404(record_id)
    if record.is_paid:
        flash('该订单已支付', 'info')
        return redirect(url_for('dashboard'))
    
    qrcodes = PaymentQRCode.query.filter_by(is_active=True).all()
    return render_template('payment.html', record=record, qrcodes=qrcodes)

# API路由
@app.route('/api/upload_image', methods=['POST'])
@login_required
def upload_image():
    if 'image' not in request.files:
        return jsonify({'success': False, 'message': '没有上传文件'})
    
    file = request.files['image']
    if file.filename == '':
        return jsonify({'success': False, 'message': '没有选择文件'})
    
    if file:
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # 识别车牌
        license_plate, message = recognizer.recognize_from_image(filepath)
        
        if license_plate:
            return jsonify({
                'success': True, 
                'license_plate': license_plate,
                'message': message,
                'image_path': filepath
            })
        else:
            return jsonify({'success': False, 'message': message})

@app.route('/api/camera_capture', methods=['POST'])
@login_required
def camera_capture():
    try:
        license_plate, message = recognizer.recognize_from_camera()
        
        if license_plate:
            return jsonify({
                'success': True,
                'license_plate': license_plate,
                'message': message
            })
        else:
            return jsonify({'success': False, 'message': message})
    except Exception as e:
        return jsonify({'success': False, 'message': f'摄像头错误: {str(e)}'})

@app.route('/api/manual_entry', methods=['POST'])
@login_required
def manual_entry():
    data = request.get_json()
    license_plate = data.get('license_plate', '').upper()
    
    if not license_plate:
        return jsonify({'success': False, 'message': '车牌号不能为空'})
    
    # 检查是否已经在场
    existing = ParkingRecord.query.filter_by(
        license_plate=license_plate, 
        exit_time=None
    ).first()
    
    if existing:
        return jsonify({'success': False, 'message': '该车辆已在场内'})
    
    # 创建或更新车辆信息
    vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
    if not vehicle:
        vehicle = Vehicle(
            license_plate=license_plate,
            owner_name=data.get('owner_name', ''),
            phone=data.get('owner_phone', ''),
            is_member=data.get('is_member', False)
        )
        db.session.add(vehicle)
    else:
        if data.get('owner_name'):
            vehicle.owner_name = data.get('owner_name')
        if data.get('owner_phone'):
            vehicle.phone = data.get('owner_phone')
        vehicle.is_member = data.get('is_member', False)
    
    # 创建停车记录
    record = ParkingRecord(
        license_plate=license_plate,
        entry_time=datetime.now(),
        entry_method='manual'
    )
    
    db.session.add(record)
    db.session.commit()
    
    return jsonify({'success': True, 'message': '入场成功'})

def calculate_parking_fee(entry_time, exit_time, is_member=False):
    """计算停车费用"""
    pricing = PricingConfig.query.filter_by(is_active=True).first()
    if not pricing:
        return 0
    
    # 计算停车时长（分钟）
    duration = (exit_time - entry_time).total_seconds() / 60
    
    # 免费时间
    if duration <= pricing.free_minutes:
        return 0
    
    # 计算收费时长（小时，向上取整）
    billable_hours = max(1, int((duration - pricing.free_minutes) / 60) + 
                        (1 if (duration - pricing.free_minutes) % 60 > 0 else 0))
    
    # 基础费用
    fee = billable_hours * pricing.hourly_rate
    
    # 日最高限额
    if pricing.daily_max and fee > pricing.daily_max:
        fee = pricing.daily_max
    
    # 会员折扣
    if is_member:
        vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
        if vehicle and vehicle.member_discount:
            fee = fee * vehicle.member_discount
    
    return float(fee)

if __name__ == '__main__':
    print("=" * 50)
    print("写字楼停车管理系统 (SQLite演示版)")
    print("=" * 50)
    
    with app.app_context():
        # 创建表
        db.create_all()
        print("数据库表创建成功!")
        
        # 创建默认管理员账户
        if not Admin.query.filter_by(username='admin').first():
            admin = Admin(
                username='admin',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin)
            print("默认管理员账户已创建: admin/admin123")
        
        # 创建默认价格配置
        if not PricingConfig.query.filter_by(is_active=True).first():
            default_pricing = PricingConfig(
                config_name='默认价格配置',
                hourly_rate=5.0,
                daily_max=50.0,
                free_minutes=15,
                is_active=True
            )
            db.session.add(default_pricing)
            print("默认价格配置已创建: 5元/小时，日最高50元，免费15分钟")
        
        db.session.commit()
        print("数据初始化完成!")
    
    print("\n" + "=" * 50)
    print("系统启动成功!")
    print("访问地址: http://localhost:5000")
    print("管理员账户: admin / admin123")
    print("按 Ctrl+C 停止服务")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
