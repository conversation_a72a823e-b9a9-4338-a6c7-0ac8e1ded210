{% extends "base.html" %}

{% block title %}统计报表 - 写字楼停车管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-chart-bar"></i> 统计报表</h2>
        <hr>
    </div>
</div>

<!-- 时间筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form class="row g-3" id="filterForm">
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">开始日期</label>
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">结束日期</label>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                    <div class="col-md-3">
                        <label for="reportType" class="form-label">报表类型</label>
                        <select class="form-control" id="reportType">
                            <option value="daily">日报表</option>
                            <option value="weekly">周报表</option>
                            <option value="monthly">月报表</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 查询
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 统计概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalEntries">0</h4>
                        <p class="mb-0">总入场次数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-sign-in-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalExits">0</h4>
                        <p class="mb-0">总出场次数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-sign-out-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalRevenue">¥0.00</h4>
                        <p class="mb-0">总收入</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="avgDuration">0小时</h4>
                        <p class="mb-0">平均停车时长</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 图表区域 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-line"></i> 收入趋势</h5>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-bar"></i> 停车次数统计</h5>
            </div>
            <div class="card-body">
                <canvas id="parkingChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> 支付方式分布</h5>
            </div>
            <div class="card-body">
                <canvas id="paymentChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-users"></i> 会员vs普通用户</h5>
            </div>
            <div class="card-body">
                <canvas id="memberChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- 详细数据表格 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-table"></i> 详细记录</h5>
                <button class="btn btn-success btn-sm" onclick="exportData()">
                    <i class="fas fa-download"></i> 导出Excel
                </button>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="detailTable">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>车牌号</th>
                                <th>入场时间</th>
                                <th>出场时间</th>
                                <th>停车时长</th>
                                <th>停车费用</th>
                                <th>支付方式</th>
                                <th>是否会员</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="分页导航">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let revenueChart, parkingChart, paymentChart, memberChart;
let currentPage = 1;
const pageSize = 20;

// 初始化日期
function initializeDates() {
    const today = new Date();
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    
    document.getElementById('startDate').value = lastWeek.toISOString().split('T')[0];
    document.getElementById('endDate').value = today.toISOString().split('T')[0];
}

// 查询数据
document.getElementById('filterForm').addEventListener('submit', function(e) {
    e.preventDefault();
    loadStatistics();
});

// 加载统计数据
function loadStatistics() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const reportType = document.getElementById('reportType').value;
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        report_type: reportType
    });
    
    fetch('/api/statistics?' + params)
    .then(response => response.json())
    .then(data => {
        updateOverview(data.overview);
        updateCharts(data.charts);
        loadDetailData(1);
    })
    .catch(error => {
        console.error('加载统计数据失败:', error);
    });
}

// 更新概览数据
function updateOverview(overview) {
    document.getElementById('totalEntries').textContent = overview.total_entries;
    document.getElementById('totalExits').textContent = overview.total_exits;
    document.getElementById('totalRevenue').textContent = '¥' + overview.total_revenue.toFixed(2);
    document.getElementById('avgDuration').textContent = overview.avg_duration;
}

// 更新图表
function updateCharts(chartData) {
    // 收入趋势图
    if (revenueChart) revenueChart.destroy();
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    revenueChart = new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: chartData.revenue.labels,
            datasets: [{
                label: '收入 (元)',
                data: chartData.revenue.data,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 停车次数统计
    if (parkingChart) parkingChart.destroy();
    const parkingCtx = document.getElementById('parkingChart').getContext('2d');
    parkingChart = new Chart(parkingCtx, {
        type: 'bar',
        data: {
            labels: chartData.parking.labels,
            datasets: [{
                label: '停车次数',
                data: chartData.parking.data,
                backgroundColor: 'rgba(54, 162, 235, 0.5)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // 支付方式分布
    if (paymentChart) paymentChart.destroy();
    const paymentCtx = document.getElementById('paymentChart').getContext('2d');
    paymentChart = new Chart(paymentCtx, {
        type: 'pie',
        data: {
            labels: chartData.payment.labels,
            datasets: [{
                data: chartData.payment.data,
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0'
                ]
            }]
        },
        options: {
            responsive: true
        }
    });
    
    // 会员vs普通用户
    if (memberChart) memberChart.destroy();
    const memberCtx = document.getElementById('memberChart').getContext('2d');
    memberChart = new Chart(memberCtx, {
        type: 'doughnut',
        data: {
            labels: ['会员', '普通用户'],
            datasets: [{
                data: chartData.member.data,
                backgroundColor: ['#4BC0C0', '#FF6384']
            }]
        },
        options: {
            responsive: true
        }
    });
}

// 加载详细数据
function loadDetailData(page) {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate,
        page: page,
        page_size: pageSize
    });
    
    fetch('/api/detail_records?' + params)
    .then(response => response.json())
    .then(data => {
        updateDetailTable(data.records);
        updatePagination(data.total_pages, page);
        currentPage = page;
    });
}

// 更新详细数据表格
function updateDetailTable(records) {
    const tbody = document.querySelector('#detailTable tbody');
    tbody.innerHTML = '';
    
    records.forEach(record => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${record.date}</td>
            <td>${record.license_plate}</td>
            <td>${record.entry_time}</td>
            <td>${record.exit_time || '未出场'}</td>
            <td>${record.duration || '-'}</td>
            <td>¥${record.parking_fee || '0.00'}</td>
            <td>${record.payment_method || '未支付'}</td>
            <td>
                <span class="badge bg-${record.is_member ? 'success' : 'secondary'}">
                    ${record.is_member ? '会员' : '普通'}
                </span>
            </td>
        `;
    });
}

// 更新分页
function updatePagination(totalPages, currentPage) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="loadDetailData(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="loadDetailData(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="loadDetailData(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 导出数据
function exportData() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    
    const params = new URLSearchParams({
        start_date: startDate,
        end_date: endDate
    });
    
    window.open('/api/export_excel?' + params, '_blank');
}

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    initializeDates();
    loadStatistics();
});
</script>
{% endblock %}
