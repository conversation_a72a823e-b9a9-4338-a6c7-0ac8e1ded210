#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys

def init_easyocr():
    """初始化EasyOCR，下载必要的模型文件"""
    print("🔄 开始初始化EasyOCR...")
    print("📥 首次使用需要下载模型文件，请耐心等待...")
    print("🌐 确保网络连接正常")
    print("-" * 50)

    try:
        # 导入EasyOCR
        print("📦 导入EasyOCR模块...")
        import easyocr
        print("✅ EasyOCR模块导入成功")

        # 创建Reader实例（这会触发模型下载）
        print("🔧 创建EasyOCR Reader实例...")
        print("📋 支持语言: 中文简体 + 英文")
        print("🚀 使用GPU模式（更快的识别速度）")

        # 创建Reader，支持中文和英文，启用GPU加速
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=True, download_enabled=True)
        print("✅ EasyOCR Reader创建成功！")

        # 测试基本功能
        print("\n🧪 测试EasyOCR基本功能...")
        import numpy as np
        import cv2

        # 创建一个简单的测试图像
        test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
        cv2.putText(test_image, "TEST123", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)

        # 进行文本识别测试
        results = reader.readtext(test_image)
        print(f"🎯 测试识别结果: {results}")

        if results:
            for (bbox, text, confidence) in results:
                print(f"   检测到文本: '{text}' (置信度: {confidence:.2f})")

        print("\n🎉 EasyOCR初始化完成！")
        print("✅ 模型文件已下载并缓存")
        print("✅ 基本功能测试通过")

        return True

    except ImportError as e:
        print(f"❌ EasyOCR模块未安装: {e}")
        print("💡 请运行: pip install easyocr")
        return False

    except Exception as e:
        print(f"❌ EasyOCR初始化失败: {e}")
        print("💡 可能的原因:")
        print("   1. 网络连接问题（无法下载模型）")
        print("   2. 磁盘空间不足")
        print("   3. 防火墙阻止下载")
        print("   4. 权限问题")
        return False

def check_easyocr_status():
    """检查EasyOCR的安装和配置状态"""
    print("🔍 检查EasyOCR状态...")

    try:
        import easyocr
        print("✅ EasyOCR已安装")
        print(f"📦 版本: {easyocr.__version__}")

        # 检查模型缓存目录
        import os
        home_dir = os.path.expanduser("~")
        easyocr_dir = os.path.join(home_dir, ".EasyOCR")

        if os.path.exists(easyocr_dir):
            print(f"📁 模型缓存目录: {easyocr_dir}")

            # 列出已下载的模型
            model_dirs = []
            if os.path.exists(os.path.join(easyocr_dir, "model")):
                model_dir = os.path.join(easyocr_dir, "model")
                model_files = os.listdir(model_dir)
                print(f"📋 已下载的模型文件: {len(model_files)} 个")
                for f in model_files[:5]:  # 只显示前5个
                    print(f"   - {f}")
                if len(model_files) > 5:
                    print(f"   ... 还有 {len(model_files) - 5} 个文件")
        else:
            print("⚠️ 模型缓存目录不存在，需要首次初始化")

        return True

    except ImportError:
        print("❌ EasyOCR未安装")
        return False

if __name__ == "__main__":
    print("🚀 EasyOCR初始化工具")
    print("=" * 50)

    # 检查当前状态
    check_easyocr_status()

    print("\n" + "=" * 50)

    # 进行初始化
    success = init_easyocr()

    print("\n" + "=" * 50)
    if success:
        print("🎉 初始化完成！现在可以使用车牌识别功能了")
    else:
        print("❌ 初始化失败，请检查错误信息并重试")
