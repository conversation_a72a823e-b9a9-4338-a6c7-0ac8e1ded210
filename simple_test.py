import os
import cv2
import numpy as np

def test_basic_image_processing():
    print("🔍 测试基本图像处理...")
    
    # 检查上传目录中的图片
    upload_dir = "static/uploads"
    if os.path.exists(upload_dir):
        files = [f for f in os.listdir(upload_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        if files:
            test_file = os.path.join(upload_dir, files[0])
            print(f"📸 测试图片: {test_file}")
            
            # 读取图像
            image = cv2.imread(test_file)
            if image is not None:
                print(f"✅ 图像读取成功，尺寸: {image.shape}")
                
                # 转换为灰度图
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
                print(f"✅ 灰度转换成功，尺寸: {gray.shape}")
                
                # 边缘检测
                edges = cv2.Canny(gray, 30, 200)
                print(f"✅ 边缘检测完成")
                
                # 查找轮廓
                contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
                print(f"✅ 找到 {len(contours)} 个轮廓")
                
                return True
            else:
                print("❌ 无法读取图像文件")
                return False
        else:
            print("❌ 上传目录中没有图片文件")
    else:
        print("❌ 上传目录不存在")
    
    return False

def test_easyocr_import():
    print("\n🔧 测试EasyOCR导入...")
    try:
        import easyocr
        print("✅ EasyOCR导入成功")
        return True
    except ImportError as e:
        print(f"❌ EasyOCR导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ EasyOCR导入出错: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始简单测试...")
    
    # 测试基本图像处理
    basic_ok = test_basic_image_processing()
    
    # 测试EasyOCR导入
    easyocr_ok = test_easyocr_import()
    
    print(f"\n📊 测试结果:")
    print(f"   基本图像处理: {'✅' if basic_ok else '❌'}")
    print(f"   EasyOCR导入: {'✅' if easyocr_ok else '❌'}")
