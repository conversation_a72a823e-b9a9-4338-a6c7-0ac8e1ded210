import os
import sys
from license_plate_recognition import LicensePlateRecognizer

def test_recognition():
    print("🔍 测试车牌识别功能...")
    
    # 初始化识别器
    recognizer = LicensePlateRecognizer()
    
    # 检查上传目录中的图片
    upload_dir = "static/uploads"
    if os.path.exists(upload_dir):
        files = [f for f in os.listdir(upload_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        if files:
            test_file = os.path.join(upload_dir, files[0])
            print(f"📸 测试图片: {test_file}")
            
            # 测试识别
            license_plate, message = recognizer.recognize_from_image(test_file)
            
            print(f"🎯 识别结果: {license_plate}")
            print(f"📝 消息: {message}")
            
            return license_plate, message
        else:
            print("❌ 上传目录中没有图片文件")
    else:
        print("❌ 上传目录不存在")
    
    return None, "没有测试图片"

def test_ocr_init():
    print("\n🔧 测试EasyOCR初始化...")
    recognizer = LicensePlateRecognizer()
    success = recognizer.try_init_ocr()
    print(f"✅ EasyOCR初始化结果: {success}")
    return success

if __name__ == "__main__":
    test_ocr_init()
    test_recognition()
