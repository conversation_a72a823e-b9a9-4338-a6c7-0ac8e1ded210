import os
import cv2
from license_plate_recognition import LicensePlateRecognizer

def test_specific_image():
    print("🔍 测试特定图片的车牌识别...")
    
    # 使用上传的图片
    image_path = "static/uploads/91f5c38c-cc1e-4762-bd4b-c035b0e49b33.png"
    
    if not os.path.exists(image_path):
        print(f"❌ 图片文件不存在: {image_path}")
        return
    
    print(f"📸 测试图片: {image_path}")
    
    # 初始化识别器
    recognizer = LicensePlateRecognizer()
    
    # 测试识别
    print("🔄 开始识别...")
    license_plate, message = recognizer.recognize_from_image(image_path)
    
    print(f"🎯 识别结果: {license_plate}")
    print(f"📝 消息: {message}")
    
    # 如果识别失败，尝试显示图片信息
    if not license_plate:
        image = cv2.imread(image_path)
        if image is not None:
            print(f"📊 图片信息:")
            print(f"   尺寸: {image.shape}")
            print(f"   类型: {image.dtype}")
            
            # 显示图片的一些统计信息
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            print(f"   灰度范围: {gray.min()} - {gray.max()}")
            print(f"   平均亮度: {gray.mean():.2f}")

def test_easyocr_initialization():
    print("\n🔧 测试EasyOCR初始化...")
    
    recognizer = LicensePlateRecognizer()
    
    print("🔄 尝试初始化EasyOCR...")
    success = recognizer.try_init_ocr()
    
    if success:
        print("✅ EasyOCR初始化成功！")
        
        # 测试简单的文本识别
        try:
            import numpy as np
            # 创建一个简单的测试图像
            test_image = np.ones((100, 300, 3), dtype=np.uint8) * 255
            cv2.putText(test_image, "TEST123", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
            
            results = recognizer.ocr.readtext(test_image)
            print(f"🧪 测试识别结果: {results}")
            
        except Exception as e:
            print(f"⚠️ 测试识别时出错: {e}")
    else:
        print("❌ EasyOCR初始化失败")

if __name__ == "__main__":
    print("🚀 开始车牌识别测试...")
    
    # 先测试EasyOCR初始化
    test_easyocr_initialization()
    
    # 然后测试具体图片
    test_specific_image()
