<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>停车费支付 - 写字楼停车管理系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        .payment-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin: 0 auto;
            max-width: 500px;
        }
        .payment-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        .payment-header i {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 1rem;
        }
        .parking-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 2rem;
        }
        .fee-amount {
            font-size: 2rem;
            font-weight: bold;
            color: #e74c3c;
            text-align: center;
            margin: 1rem 0;
        }
        .payment-methods {
            display: flex;
            gap: 1rem;
            margin-bottom: 2rem;
        }
        .payment-method {
            flex: 1;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        .payment-method:hover {
            border-color: #667eea;
            background: #f8f9fa;
        }
        .payment-method.active {
            border-color: #667eea;
            background: #e3f2fd;
        }
        .payment-method i {
            font-size: 2rem;
            margin-bottom: 0.5rem;
        }
        .wechat-color { color: #1aad19; }
        .alipay-color { color: #1677ff; }
        .qr-code-container {
            text-align: center;
            padding: 2rem;
            background: #f8f9fa;
            border-radius: 10px;
            margin-bottom: 2rem;
        }
        .qr-code-image {
            max-width: 200px;
            height: auto;
            border: 1px solid #ddd;
        }
        .payment-status {
            text-align: center;
            margin-top: 2rem;
        }
        .countdown {
            font-size: 1.2rem;
            color: #e74c3c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="payment-card">
            <div class="payment-header">
                <i class="fas fa-credit-card"></i>
                <h3>停车费支付</h3>
                <p class="text-muted">请选择支付方式完成付款</p>
            </div>

            <!-- 停车信息 -->
            <div class="parking-info">
                <div class="row">
                    <div class="col-6">
                        <strong>车牌号：</strong><br>
                        <span class="text-primary">{{ record.license_plate }}</span>
                    </div>
                    <div class="col-6">
                        <strong>入场时间：</strong><br>
                        {{ record.entry_time.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-6">
                        <strong>出场时间：</strong><br>
                        {{ record.exit_time.strftime('%Y-%m-%d %H:%M') }}
                    </div>
                    <div class="col-6">
                        <strong>停车时长：</strong><br>
                        <span id="parkingDuration"></span>
                    </div>
                </div>
            </div>

            <!-- 费用金额 -->
            <div class="fee-amount">
                ¥{{ "%.2f"|format(record.parking_fee) }}
            </div>

            <!-- 支付方式选择 -->
            <div class="payment-methods">
                {% for qrcode in qrcodes %}
                    {% if qrcode.payment_type == 'wechat' %}
                        <div class="payment-method" onclick="selectPaymentMethod('wechat')" id="wechat-method">
                            <i class="fab fa-weixin wechat-color"></i>
                            <div>微信支付</div>
                        </div>
                    {% elif qrcode.payment_type == 'alipay' %}
                        <div class="payment-method" onclick="selectPaymentMethod('alipay')" id="alipay-method">
                            <i class="fab fa-alipay alipay-color"></i>
                            <div>支付宝</div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>

            <!-- 二维码显示区域 -->
            <div id="qrCodeContainer" class="qr-code-container" style="display: none;">
                <div id="qrCodeContent">
                    <p><strong>请使用手机扫描二维码完成支付</strong></p>
                    <img id="qrCodeImage" src="" alt="支付二维码" class="qr-code-image">
                    <div class="mt-3">
                        <small class="text-muted">支付金额：¥{{ "%.2f"|format(record.parking_fee) }}</small>
                    </div>
                </div>
            </div>

            <!-- 支付状态 -->
            <div class="payment-status">
                <div class="countdown" id="countdown">
                    支付倒计时：<span id="countdownTime">10:00</span>
                </div>
                <div class="mt-3">
                    <button class="btn btn-success btn-lg" onclick="confirmPayment()">
                        <i class="fas fa-check"></i> 确认已支付
                    </button>
                    <button class="btn btn-secondary btn-lg ms-2" onclick="window.close()">
                        <i class="fas fa-times"></i> 取消
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const qrcodes = {
            {% for qrcode in qrcodes %}
                '{{ qrcode.payment_type }}': '{{ url_for("static", filename=qrcode.qr_image_path.replace("static/", "")) }}',
            {% endfor %}
        };

        let countdownTimer;
        let timeLeft = 600; // 10分钟

        // 计算停车时长
        function calculateDuration() {
            const entryTime = new Date('{{ record.entry_time.strftime("%Y-%m-%d %H:%M:%S") }}');
            const exitTime = new Date('{{ record.exit_time.strftime("%Y-%m-%d %H:%M:%S") }}');
            const duration = exitTime - entryTime;
            
            const hours = Math.floor(duration / (1000 * 60 * 60));
            const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
            
            document.getElementById('parkingDuration').textContent = `${hours}小时${minutes}分钟`;
        }

        // 选择支付方式
        function selectPaymentMethod(method) {
            // 移除所有active类
            document.querySelectorAll('.payment-method').forEach(el => {
                el.classList.remove('active');
            });
            
            // 添加active类到选中的方法
            document.getElementById(method + '-method').classList.add('active');
            
            // 显示对应的二维码
            if (qrcodes[method]) {
                document.getElementById('qrCodeImage').src = qrcodes[method];
                document.getElementById('qrCodeContainer').style.display = 'block';
                
                // 开始倒计时
                startCountdown();
            }
        }

        // 开始倒计时
        function startCountdown() {
            if (countdownTimer) {
                clearInterval(countdownTimer);
            }
            
            countdownTimer = setInterval(function() {
                timeLeft--;
                
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                
                document.getElementById('countdownTime').textContent = 
                    `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                
                if (timeLeft <= 0) {
                    clearInterval(countdownTimer);
                    alert('支付超时，请重新选择支付方式');
                    document.getElementById('qrCodeContainer').style.display = 'none';
                    timeLeft = 600;
                }
            }, 1000);
        }

        // 确认支付
        function confirmPayment() {
            if (confirm('请确认您已完成支付？')) {
                fetch('/api/confirm_payment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        record_id: {{ record.id }},
                        payment_method: document.querySelector('.payment-method.active') ? 
                            document.querySelector('.payment-method.active').id.replace('-method', '') : null
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('支付确认成功！');
                        window.close();
                    } else {
                        alert('支付确认失败：' + data.message);
                    }
                });
            }
        }

        // 页面加载时计算停车时长
        document.addEventListener('DOMContentLoaded', function() {
            calculateDuration();
        });
    </script>
</body>
</html>
