#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 测试EasyOCR车牌识别系统
"""

import os
import sys

def test_imports():
    """测试关键模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        from flask import Flask
        print("✅ Flask导入成功")
    except ImportError as e:
        print(f"❌ Flask导入失败: {e}")
        return False
    
    try:
        from license_plate_recognition import LicensePlateRecognizer
        print("✅ 车牌识别模块导入成功")
    except ImportError as e:
        print(f"❌ 车牌识别模块导入失败: {e}")
        return False
    
    try:
        import easyocr
        print("✅ EasyOCR模块可用")
    except ImportError:
        print("⚠️ EasyOCR模块不可用，将使用手动输入模式")
    
    return True

def test_license_recognition():
    """测试车牌识别器初始化"""
    print("\n🔍 测试车牌识别器...")
    
    try:
        from license_plate_recognition import LicensePlateRecognizer
        recognizer = LicensePlateRecognizer()
        print("✅ 车牌识别器初始化成功")
        return True
    except Exception as e:
        print(f"❌ 车牌识别器初始化失败: {e}")
        return False

def start_server():
    """启动服务器"""
    print("\n🚀 启动Flask服务器...")
    
    try:
        from app import app
        
        print("\n" + "=" * 60)
        print("🎉 写字楼停车管理系统启动成功!")
        print("📱 访问地址: http://localhost:5000")
        print("👤 管理员账户: admin")
        print("🔑 管理员密码: admin123")
        print("🔧 车牌识别: 支持EasyOCR自动识别和手动输入")
        print("⚠️  按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n✅ 系统已安全停止")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("🚗 写字楼停车管理系统 - EasyOCR版本")
    print("=" * 60)
    
    # 测试模块导入
    if not test_imports():
        print("\n❌ 模块导入测试失败")
        input("按回车键退出...")
        return False
    
    # 测试车牌识别器
    if not test_license_recognition():
        print("\n❌ 车牌识别器测试失败")
        input("按回车键退出...")
        return False
    
    print("\n✅ 所有测试通过，准备启动服务器...")
    
    # 启动服务器
    return start_server()

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except Exception as e:
        print(f"启动脚本出错: {e}")
        input("按回车键退出...")
        sys.exit(1)
