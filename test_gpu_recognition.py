#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
from license_plate_recognition import LicensePlateRecognizer

def test_gpu_recognition():
    """测试GPU版本的车牌识别"""
    print("🚀 测试GPU版本车牌识别...")
    print("=" * 50)
    
    # 初始化识别器
    recognizer = LicensePlateRecognizer()
    
    # 测试图片路径
    image_path = "static/uploads/91f5c38c-cc1e-4762-bd4b-c035b0e49b33.png"
    
    if not os.path.exists(image_path):
        print(f"❌ 测试图片不存在: {image_path}")
        return False
    
    print(f"📸 测试图片: {image_path}")
    
    # 尝试识别
    print("\n🔄 开始车牌识别...")
    try:
        license_plate, message = recognizer.recognize_from_image(image_path)
        
        print(f"\n📊 识别结果:")
        print(f"   车牌号: {license_plate}")
        print(f"   消息: {message}")
        
        if license_plate:
            print("✅ 识别成功！")
            return True
        else:
            print("⚠️ 识别失败，但程序运行正常")
            return False
            
    except Exception as e:
        print(f"❌ 识别过程出错: {e}")
        return False

def test_gpu_availability():
    """测试GPU可用性"""
    print("\n🔍 检查GPU环境...")
    
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"🔍 CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"🎯 GPU设备数量: {torch.cuda.device_count()}")
            print(f"📱 当前GPU设备: {torch.cuda.get_device_name(0)}")
            print(f"💾 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        return torch.cuda.is_available()
        
    except ImportError:
        print("❌ PyTorch未安装")
        return False
    except Exception as e:
        print(f"❌ GPU检查出错: {e}")
        return False

if __name__ == "__main__":
    print("🚀 GPU车牌识别测试工具")
    print("=" * 60)
    
    # 检查GPU环境
    gpu_ok = test_gpu_availability()
    
    print("\n" + "=" * 60)
    
    # 测试车牌识别
    recognition_ok = test_gpu_recognition()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   GPU环境: {'✅' if gpu_ok else '❌'}")
    print(f"   车牌识别: {'✅' if recognition_ok else '⚠️'}")
    
    if gpu_ok and recognition_ok:
        print("\n🎉 GPU车牌识别功能正常！")
    elif gpu_ok:
        print("\n⚠️ GPU可用，但车牌识别需要调试")
    else:
        print("\n💻 将使用CPU模式进行车牌识别")
