#!/usr/bin/env python3
"""
最简单的会员功能测试
"""
from flask import Flask, jsonify, request
import pymysql
from decimal import Decimal

app = Flask(__name__)

@app.route('/')
def home():
    return """
    <h1>会员功能测试</h1>
    <button onclick="testAddMember()">测试添加会员</button>
    <button onclick="testGetMembers()">获取会员列表</button>
    <div id="result"></div>
    
    <script>
    function testAddMember() {
        fetch('/api/add_member', {
            method: 'POST',
            headers: {'Content-Type': 'application/json'},
            body: JSON.stringify({
                license_plate: '测试B456',
                owner_name: '测试用户2',
                phone: '13900139000',
                member_discount: 0.8
            })
        })
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = '<p>添加会员结果: ' + JSON.stringify(data) + '</p>';
        });
    }
    
    function testGetMembers() {
        fetch('/api/members')
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = '<p>会员列表: ' + JSON.stringify(data) + '</p>';
        });
    }
    </script>
    """

@app.route('/api/add_member', methods=['POST'])
def add_member():
    try:
        data = request.get_json()
        print(f"收到请求: {data}")
        
        license_plate = data.get('license_plate', '').upper()
        owner_name = data.get('owner_name', '')
        phone = data.get('phone', '')
        member_discount = data.get('member_discount', 0.9)
        
        # 直接操作数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='car-park',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # 检查是否已存在
            cursor.execute("SELECT * FROM vehicles WHERE license_plate = %s", (license_plate,))
            existing = cursor.fetchone()
            
            if existing:
                cursor.execute("""
                    UPDATE vehicles 
                    SET owner_name = %s, phone = %s, is_member = 1, member_discount = %s 
                    WHERE license_plate = %s
                """, (owner_name, phone, member_discount, license_plate))
            else:
                cursor.execute("""
                    INSERT INTO vehicles (license_plate, owner_name, phone, is_member, member_discount, created_at)
                    VALUES (%s, %s, %s, 1, %s, NOW())
                """, (license_plate, owner_name, phone, member_discount))
            
            connection.commit()
        
        connection.close()
        print("会员添加成功")
        return jsonify({'success': True, 'message': '会员添加成功'})
        
    except Exception as e:
        print(f"错误: {e}")
        return jsonify({'success': False, 'message': str(e)})

@app.route('/api/members')
def get_members():
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='car-park',
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT license_plate, owner_name, phone, member_discount FROM vehicles WHERE is_member = 1")
            members = cursor.fetchall()
            
            result = []
            for member in members:
                result.append({
                    'license_plate': member[0],
                    'owner_name': member[1],
                    'phone': member[2],
                    'member_discount': float(member[3])
                })
        
        connection.close()
        return jsonify({'members': result})
        
    except Exception as e:
        print(f"错误: {e}")
        return jsonify({'members': []})

if __name__ == '__main__':
    print("启动最简单的会员测试应用...")
    print("访问: http://localhost:5003")
    app.run(debug=True, host='0.0.0.0', port=5003)
