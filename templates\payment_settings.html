{% extends "base.html" %}

{% block title %}支付设置 - 写字楼停车管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-qrcode"></i> 支付设置</h2>
        <hr>
    </div>
</div>

<div class="row">
    <!-- 微信支付设置 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fab fa-weixin text-success"></i> 微信支付二维码</h5>
            </div>
            <div class="card-body">
                {% set wechat_qr = qrcodes|selectattr("payment_type", "equalto", "wechat")|first %}
                
                {% if wechat_qr %}
                <div class="text-center mb-3">
                    <img src="{{ url_for('static', filename=wechat_qr.qr_image_path.replace('static/', '')) }}" 
                         alt="微信支付二维码" class="img-fluid" style="max-width: 200px;">
                    <p class="mt-2 text-muted">当前微信支付二维码</p>
                    <small class="text-muted">上传时间：{{ wechat_qr.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
                {% else %}
                <div class="text-center mb-3">
                    <i class="fas fa-qrcode fa-5x text-muted"></i>
                    <p class="mt-2 text-muted">尚未上传微信支付二维码</p>
                </div>
                {% endif %}
                
                <form id="wechatForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="wechatQR" class="form-label">上传新的微信支付二维码</label>
                        <input type="file" class="form-control" id="wechatQR" accept="image/*" required>
                        <div class="form-text">支持 JPG、PNG 格式，建议尺寸 300x300 像素</div>
                    </div>
                    <div class="mb-3">
                        <img id="wechatPreview" src="" alt="预览" style="max-width: 150px; display: none;">
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i> 上传微信二维码
                    </button>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 支付宝设置 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fab fa-alipay text-primary"></i> 支付宝二维码</h5>
            </div>
            <div class="card-body">
                {% set alipay_qr = qrcodes|selectattr("payment_type", "equalto", "alipay")|first %}
                
                {% if alipay_qr %}
                <div class="text-center mb-3">
                    <img src="{{ url_for('static', filename=alipay_qr.qr_image_path.replace('static/', '')) }}" 
                         alt="支付宝二维码" class="img-fluid" style="max-width: 200px;">
                    <p class="mt-2 text-muted">当前支付宝二维码</p>
                    <small class="text-muted">上传时间：{{ alipay_qr.uploaded_at.strftime('%Y-%m-%d %H:%M') }}</small>
                </div>
                {% else %}
                <div class="text-center mb-3">
                    <i class="fas fa-qrcode fa-5x text-muted"></i>
                    <p class="mt-2 text-muted">尚未上传支付宝二维码</p>
                </div>
                {% endif %}
                
                <form id="alipayForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="alipayQR" class="form-label">上传新的支付宝二维码</label>
                        <input type="file" class="form-control" id="alipayQR" accept="image/*" required>
                        <div class="form-text">支持 JPG、PNG 格式，建议尺寸 300x300 像素</div>
                    </div>
                    <div class="mb-3">
                        <img id="alipayPreview" src="" alt="预览" style="max-width: 150px; display: none;">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-upload"></i> 上传支付宝二维码
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 使用说明 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-info-circle"></i> 使用说明</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fab fa-weixin text-success"></i> 微信支付二维码获取方法：</h6>
                        <ol>
                            <li>打开微信，进入"我" → "服务" → "收付款"</li>
                            <li>点击"二维码收款"</li>
                            <li>点击"保存收款码"或截图保存</li>
                            <li>将保存的图片上传到系统中</li>
                        </ol>
                        
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb"></i>
                            <strong>提示：</strong>建议使用商户收款码，支持信用卡付款，到账更稳定。
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <h6><i class="fab fa-alipay text-primary"></i> 支付宝二维码获取方法：</h6>
                        <ol>
                            <li>打开支付宝，点击首页"收钱"</li>
                            <li>点击"保存图片"或截图保存</li>
                            <li>将保存的图片上传到系统中</li>
                        </ol>
                        
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>注意：</strong>个人收款码有单日限额，建议申请商家收款码。
                        </div>
                    </div>
                </div>
                
                <hr>
                
                <div class="row">
                    <div class="col-12">
                        <h6><i class="fas fa-cogs"></i> 系统使用流程：</h6>
                        <ol>
                            <li>车辆出场时，系统会自动计算停车费用</li>
                            <li>如果费用大于0，会弹出支付页面</li>
                            <li>用户选择支付方式（微信或支付宝）</li>
                            <li>系统显示对应的收款二维码</li>
                            <li>用户扫码支付后，点击"确认已支付"完成流程</li>
                        </ol>
                        
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            <strong>优势：</strong>无需接入复杂的支付接口，使用个人收款码即可实现收费功能。
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 支付记录统计 -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-chart-pie"></i> 支付方式统计</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <canvas id="paymentStatsChart" width="400" height="200"></canvas>
                    </div>
                    <div class="col-md-4">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>支付方式</th>
                                        <th>次数</th>
                                        <th>金额</th>
                                    </tr>
                                </thead>
                                <tbody id="paymentStatsTable">
                                    <!-- 数据将通过AJAX加载 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// 微信二维码预览
document.getElementById('wechatQR').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('wechatPreview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// 支付宝二维码预览
document.getElementById('alipayQR').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('alipayPreview');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// 微信二维码上传
document.getElementById('wechatForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    const fileInput = document.getElementById('wechatQR');
    
    if (!fileInput.files[0]) {
        alert('请选择图片文件');
        return;
    }
    
    formData.append('qrcode', fileInput.files[0]);
    formData.append('payment_type', 'wechat');
    
    fetch('/api/upload_qrcode', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('微信二维码上传成功！');
            location.reload();
        } else {
            alert('上传失败：' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中出错：' + error.message);
    });
});

// 支付宝二维码上传
document.getElementById('alipayForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    const fileInput = document.getElementById('alipayQR');
    
    if (!fileInput.files[0]) {
        alert('请选择图片文件');
        return;
    }
    
    formData.append('qrcode', fileInput.files[0]);
    formData.append('payment_type', 'alipay');
    
    fetch('/api/upload_qrcode', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('支付宝二维码上传成功！');
            location.reload();
        } else {
            alert('上传失败：' + data.message);
        }
    })
    .catch(error => {
        alert('上传过程中出错：' + error.message);
    });
});

// 加载支付统计数据
function loadPaymentStats() {
    fetch('/api/payment_stats')
    .then(response => response.json())
    .then(data => {
        updatePaymentChart(data);
        updatePaymentTable(data);
    })
    .catch(error => {
        console.error('加载支付统计失败:', error);
    });
}

// 更新支付统计图表
function updatePaymentChart(data) {
    const ctx = document.getElementById('paymentStatsChart').getContext('2d');
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: data.labels,
            datasets: [{
                data: data.amounts,
                backgroundColor: [
                    '#1aad19',  // 微信绿
                    '#1677ff',  // 支付宝蓝
                    '#ffa500',  // 现金橙
                    '#6c757d'   // 其他灰
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

// 更新支付统计表格
function updatePaymentTable(data) {
    const tbody = document.getElementById('paymentStatsTable');
    tbody.innerHTML = '';
    
    data.details.forEach(item => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>${item.method}</td>
            <td>${item.count}</td>
            <td>¥${item.amount.toFixed(2)}</td>
        `;
    });
}

// 页面加载时获取支付统计
document.addEventListener('DOMContentLoaded', function() {
    loadPaymentStats();
});
</script>
{% endblock %}
