{% extends "base.html" %}

{% block title %}控制台 - 写字楼停车管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-tachometer-alt"></i> 控制台</h2>
        <hr>
    </div>
</div>

<!-- 统计卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.today_entries }}</h4>
                        <p class="mb-0">今日入场</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-sign-in-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.today_exits }}</h4>
                        <p class="mb-0">今日出场</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-sign-out-alt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ stats.current_parked }}</h4>
                        <p class="mb-0">当前在场</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-car fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>¥{{ "%.2f"|format(stats.today_revenue) }}</h4>
                        <p class="mb-0">今日收入</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bolt"></i> 快速操作</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('vehicle_recognition') }}" class="btn btn-primary btn-lg w-100">
                            <i class="fas fa-camera"></i><br>
                            车辆识别
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-success btn-lg w-100" onclick="manualEntry()">
                            <i class="fas fa-plus"></i><br>
                            手动入场
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-warning btn-lg w-100" onclick="manualExit()">
                            <i class="fas fa-minus"></i><br>
                            手动出场
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('statistics') }}" class="btn btn-info btn-lg w-100">
                            <i class="fas fa-chart-bar"></i><br>
                            查看报表
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 最近记录 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-history"></i> 最近停车记录</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped" id="recentRecords">
                        <thead>
                            <tr>
                                <th>车牌号</th>
                                <th>入场时间</th>
                                <th>出场时间</th>
                                <th>停车费用</th>
                                <th>支付状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- 数据将通过AJAX加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 手动入场模态框 -->
<div class="modal fade" id="manualEntryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">手动入场</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="manualEntryForm">
                    <div class="mb-3">
                        <label for="entryLicensePlate" class="form-label">车牌号</label>
                        <input type="text" class="form-control" id="entryLicensePlate" required>
                    </div>
                    <div class="mb-3">
                        <label for="ownerName" class="form-label">车主姓名（可选）</label>
                        <input type="text" class="form-control" id="ownerName">
                    </div>
                    <div class="mb-3">
                        <label for="ownerPhone" class="form-label">联系电话（可选）</label>
                        <input type="text" class="form-control" id="ownerPhone">
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isMember">
                        <label class="form-check-label" for="isMember">
                            会员车辆
                        </label>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="submitManualEntry()">确认入场</button>
            </div>
        </div>
    </div>
</div>

<!-- 手动出场模态框 -->
<div class="modal fade" id="manualExitModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">手动出场</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="manualExitForm">
                    <div class="mb-3">
                        <label for="exitLicensePlate" class="form-label">车牌号</label>
                        <input type="text" class="form-control" id="exitLicensePlate" required>
                        <button type="button" class="btn btn-sm btn-outline-primary mt-2" onclick="searchParkedVehicle()">
                            搜索在场车辆
                        </button>
                    </div>
                    <div id="parkingInfo" style="display: none;">
                        <div class="alert alert-info">
                            <strong>停车信息：</strong>
                            <div id="parkingDetails"></div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="submitManualExit()">确认出场</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function manualEntry() {
    $('#manualEntryModal').modal('show');
}

function manualExit() {
    $('#manualExitModal').modal('show');
}

function submitManualEntry() {
    const formData = {
        license_plate: $('#entryLicensePlate').val(),
        owner_name: $('#ownerName').val(),
        owner_phone: $('#ownerPhone').val(),
        is_member: $('#isMember').is(':checked')
    };

    fetch('/api/manual_entry', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('入场成功！');
            $('#manualEntryModal').modal('hide');
            location.reload();
        } else {
            alert('入场失败：' + data.message);
        }
    });
}

function submitManualExit() {
    const licensePlate = $('#exitLicensePlate').val();

    fetch('/api/manual_exit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({license_plate: licensePlate})
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (data.fee > 0) {
                // 显示支付页面
                window.open('/payment/' + data.record_id, '_blank');
            }
            alert('出场成功！停车费用：¥' + data.fee);
            $('#manualExitModal').modal('hide');
            location.reload();
        } else {
            alert('出场失败：' + data.message);
        }
    });
}

function searchParkedVehicle() {
    const licensePlate = $('#exitLicensePlate').val();
    if (!licensePlate) {
        alert('请输入车牌号');
        return;
    }

    fetch('/api/search_parked_vehicle/' + licensePlate)
    .then(response => response.json())
    .then(data => {
        if (data.found) {
            $('#parkingDetails').html(`
                入场时间：${data.entry_time}<br>
                停车时长：${data.duration}<br>
                预计费用：¥${data.estimated_fee}
            `);
            $('#parkingInfo').show();
        } else {
            alert('未找到该车辆的停车记录');
            $('#parkingInfo').hide();
        }
    });
}

// 加载最近记录
function loadRecentRecords() {
    fetch('/api/recent_records')
    .then(response => response.json())
    .then(data => {
        const tbody = document.querySelector('#recentRecords tbody');
        tbody.innerHTML = '';

        data.records.forEach(record => {
            const row = tbody.insertRow();
            row.innerHTML = `
                <td>${record.license_plate}</td>
                <td>${record.entry_time}</td>
                <td>${record.exit_time || '未出场'}</td>
                <td>¥${record.parking_fee || '0.00'}</td>
                <td>
                    <span class="badge bg-${record.is_paid ? 'success' : 'warning'}">
                        ${record.is_paid ? '已支付' : '未支付'}
                    </span>
                </td>
                <td>
                    ${!record.exit_time ?
                        `<button class="btn btn-sm btn-warning" onclick="quickExit('${record.license_plate}')">出场</button>` :
                        '已完成'
                    }
                </td>
            `;
        });
    });
}

function quickExit(licensePlate) {
    $('#exitLicensePlate').val(licensePlate);
    searchParkedVehicle();
    $('#manualExitModal').modal('show');
}

// 加载统计数据
function loadDashboardStats() {
    fetch('/api/dashboard_stats')
    .then(response => response.json())
    .then(data => {
        // 更新统计卡片（如果需要实时更新的话）
        console.log('Dashboard stats loaded:', data);
    })
    .catch(error => {
        console.error('加载统计数据失败:', error);
    });
}

// 页面加载时获取最近记录
document.addEventListener('DOMContentLoaded', function() {
    loadRecentRecords();
    loadDashboardStats();

    // 每30秒刷新一次数据
    setInterval(() => {
        loadRecentRecords();
        loadDashboardStats();
    }, 30000);
});
</script>
{% endblock %}
