Stack trace:
Frame         Function      Args
0007FFFFAB80  00021005FEBA (000210285F48, 00021026AB6E, 0007FFFFAB80, 0007FFFF9A80) msys-2.0.dll+0x1FEBA
0007FFFFAB80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFAE58) msys-2.0.dll+0x67F9
0007FFFFAB80  000210046832 (000210285FF9, 0007FFFFAA38, 0007FFFFAB80, 000000000000) msys-2.0.dll+0x6832
0007FFFFAB80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFAB80  0002100690B4 (0007FFFFAB90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFAE60  00021006A49D (0007FFFFAB90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFAA2620000 ntdll.dll
7FFAA1550000 KERNEL32.DLL
7FFA9FA40000 KERNELBASE.dll
7FFAA1380000 USER32.dll
7FFAA02E0000 win32u.dll
000210040000 msys-2.0.dll
7FFAA2130000 GDI32.dll
7FFA9FFB0000 gdi32full.dll
7FFAA0310000 msvcp_win.dll
7FFAA00F0000 ucrtbase.dll
7FFAA1620000 advapi32.dll
7FFAA2530000 msvcrt.dll
7FFAA1A40000 sechost.dll
7FFAA10C0000 RPCRT4.dll
7FFA9EE90000 CRYPTBASE.DLL
7FFAA0240000 bcryptPrimitives.dll
7FFAA2470000 IMM32.DLL
