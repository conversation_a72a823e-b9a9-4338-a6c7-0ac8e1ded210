#!/usr/bin/env python3
"""
直接测试会员添加功能
"""
import pymysql
from decimal import Decimal

def test_add_member():
    try:
        # 连接数据库
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='car-park',
            charset='utf8mb4'
        )
        
        print("✅ 数据库连接成功")
        
        with connection.cursor() as cursor:
            # 测试添加会员
            license_plate = "测试A123"
            owner_name = "测试用户"
            phone = "13800138000"
            member_discount = 0.9
            
            # 检查是否已存在
            cursor.execute("SELECT * FROM vehicles WHERE license_plate = %s", (license_plate,))
            existing = cursor.fetchone()
            
            if existing:
                print(f"车辆 {license_plate} 已存在，更新信息...")
                cursor.execute("""
                    UPDATE vehicles 
                    SET owner_name = %s, phone = %s, is_member = 1, member_discount = %s 
                    WHERE license_plate = %s
                """, (owner_name, phone, member_discount, license_plate))
            else:
                print(f"添加新会员 {license_plate}...")
                cursor.execute("""
                    INSERT INTO vehicles (license_plate, owner_name, phone, is_member, member_discount, created_at)
                    VALUES (%s, %s, %s, 1, %s, NOW())
                """, (license_plate, owner_name, phone, member_discount))
            
            connection.commit()
            print("✅ 会员添加成功")
            
            # 验证添加结果
            cursor.execute("SELECT * FROM vehicles WHERE license_plate = %s", (license_plate,))
            result = cursor.fetchone()
            print(f"✅ 验证结果: {result}")
            
            # 查看所有会员
            cursor.execute("SELECT license_plate, owner_name, phone, member_discount FROM vehicles WHERE is_member = 1")
            members = cursor.fetchall()
            print(f"✅ 当前所有会员: {members}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_add_member()
