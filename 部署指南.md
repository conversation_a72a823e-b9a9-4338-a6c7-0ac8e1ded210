# 写字楼停车管理系统部署指南

## 系统概述

本系统是一个基于Python Flask开发的智能停车管理系统，支持车牌识别、收费管理、会员管理等功能。

## 环境要求

### 基础环境
- **操作系统**: Windows 10/11, Linux, macOS
- **Python版本**: 3.8 或更高版本
- **数据库**: MySQL 5.7+ 或 SQLite 3.x
- **浏览器**: Chrome, Firefox, Safari, Edge (现代浏览器)

### 硬件要求
- **CPU**: 双核 2.0GHz 或更高
- **内存**: 4GB RAM 或更高
- **存储**: 10GB 可用空间
- **摄像头**: USB摄像头或网络摄像头（可选）

## 快速部署（SQLite版本）

### 1. 下载项目
```bash
# 下载项目文件到本地目录
cd /path/to/your/project
```

### 2. 安装Python依赖
```bash
pip install Flask Flask-SQLAlchemy Flask-Login PyMySQL Werkzeug opencv-python Pillow numpy python-dateutil
```

### 3. 启动系统
```bash
python app_sqlite.py
```

### 4. 访问系统
- 打开浏览器访问: http://localhost:5000
- 默认管理员账户: admin / admin123

## 生产环境部署（MySQL版本）

### 1. 安装MySQL数据库

#### Windows
1. 下载MySQL安装包
2. 运行安装程序，设置root密码为 `123456`
3. 启动MySQL服务

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install mysql-server
sudo mysql_secure_installation
```

#### Linux (CentOS/RHEL)
```bash
sudo yum install mysql-server
sudo systemctl start mysqld
sudo mysql_secure_installation
```

### 2. 创建数据库
```sql
CREATE DATABASE `car-park` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 3. 配置Python环境
```bash
# 创建虚拟环境
python -m venv parking_env

# 激活虚拟环境
# Windows:
parking_env\Scripts\activate
# Linux/macOS:
source parking_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 4. 配置应用
编辑 `app.py` 中的数据库连接配置：
```python
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:123456@localhost/car-park'
```

### 5. 启动应用
```bash
python app.py
```

## Docker部署

### 1. 创建Dockerfile
```dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 5000

CMD ["python", "app_sqlite.py"]
```

### 2. 构建镜像
```bash
docker build -t parking-system .
```

### 3. 运行容器
```bash
docker run -d -p 5000:5000 --name parking-app parking-system
```

## Nginx反向代理配置

### 1. 安装Nginx
```bash
# Ubuntu/Debian
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

### 2. 配置Nginx
创建配置文件 `/etc/nginx/sites-available/parking-system`:
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static {
        alias /path/to/your/project/static;
        expires 30d;
    }
}
```

### 3. 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/parking-system /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL证书配置

### 1. 使用Let's Encrypt
```bash
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com
```

### 2. 自动续期
```bash
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 系统服务配置

### 1. 创建systemd服务文件
创建 `/etc/systemd/system/parking-system.service`:
```ini
[Unit]
Description=Parking Management System
After=network.target

[Service]
Type=simple
User=www-data
WorkingDirectory=/path/to/your/project
Environment=PATH=/path/to/your/project/parking_env/bin
ExecStart=/path/to/your/project/parking_env/bin/python app.py
Restart=always

[Install]
WantedBy=multi-user.target
```

### 2. 启动服务
```bash
sudo systemctl daemon-reload
sudo systemctl enable parking-system
sudo systemctl start parking-system
```

## 数据库备份策略

### 1. 自动备份脚本
创建 `backup.sh`:
```bash
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backup/parking-system"
DB_NAME="car-park"

mkdir -p $BACKUP_DIR

mysqldump -u root -p123456 $DB_NAME > $BACKUP_DIR/backup_$DATE.sql

# 保留最近30天的备份
find $BACKUP_DIR -name "backup_*.sql" -mtime +30 -delete
```

### 2. 设置定时备份
```bash
sudo crontab -e
# 每天凌晨2点备份
0 2 * * * /path/to/backup.sh
```

## 监控和日志

### 1. 应用日志配置
在 `app.py` 中添加日志配置：
```python
import logging
from logging.handlers import RotatingFileHandler

if not app.debug:
    file_handler = RotatingFileHandler('logs/parking.log', maxBytes=10240, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
```

### 2. 系统监控
使用 `htop` 或 `top` 监控系统资源：
```bash
sudo apt install htop
htop
```

## 性能优化

### 1. 数据库优化
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_license_plate ON parking_records(license_plate);
CREATE INDEX idx_entry_time ON parking_records(entry_time);
CREATE INDEX idx_exit_time ON parking_records(exit_time);
```

### 2. 应用优化
- 使用Redis缓存频繁查询的数据
- 启用Gzip压缩
- 优化静态资源加载

### 3. Nginx优化
```nginx
# 启用Gzip压缩
gzip on;
gzip_vary on;
gzip_min_length 1024;
gzip_types text/plain text/css application/json application/javascript text/xml application/xml;

# 设置缓存
location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}
```

## 安全配置

### 1. 防火墙设置
```bash
# Ubuntu/Debian
sudo ufw allow 22
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. 数据库安全
```sql
-- 创建专用数据库用户
CREATE USER 'parking_user'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON `car-park`.* TO 'parking_user'@'localhost';
FLUSH PRIVILEGES;
```

### 3. 应用安全
- 修改默认管理员密码
- 设置强密码策略
- 定期更新依赖包
- 启用HTTPS

## 故障排除

### 1. 常见问题

#### 数据库连接失败
```bash
# 检查MySQL服务状态
sudo systemctl status mysql

# 检查端口是否开放
netstat -tlnp | grep 3306
```

#### 应用无法启动
```bash
# 检查Python环境
python --version
pip list

# 检查日志
tail -f logs/parking.log
```

#### 静态文件无法加载
```bash
# 检查文件权限
ls -la static/
chmod -R 755 static/
```

### 2. 性能问题
- 检查数据库查询性能
- 监控系统资源使用
- 优化数据库索引
- 增加服务器资源

## 更新和维护

### 1. 应用更新
```bash
# 备份当前版本
cp -r /path/to/current /path/to/backup

# 更新代码
git pull origin main

# 重启服务
sudo systemctl restart parking-system
```

### 2. 数据库迁移
```bash
# 备份数据库
mysqldump -u root -p car-park > backup.sql

# 执行迁移脚本
python migrate.py
```

### 3. 定期维护
- 清理过期日志文件
- 优化数据库表
- 更新系统补丁
- 检查安全漏洞

## 联系支持

如遇到部署问题，请检查：
1. 系统环境是否满足要求
2. 依赖包是否正确安装
3. 数据库配置是否正确
4. 网络和防火墙设置
5. 日志文件中的错误信息

技术支持邮箱：<EMAIL>
