#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
写字楼停车管理系统演示脚本
用于自动创建演示数据和展示系统功能
"""

import os
import sys
import time
import random
from datetime import datetime, timedelta

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_demo_data():
    """创建演示数据"""
    from app_sqlite import app, db, Admin, Vehicle, ParkingRecord, PricingConfig, PaymentQRCode
    from werkzeug.security import generate_password_hash
    from decimal import Decimal
    
    with app.app_context():
        print("正在创建演示数据...")
        
        # 清空现有数据（除了管理员和价格配置）
        ParkingRecord.query.delete()
        Vehicle.query.delete()
        PaymentQRCode.query.delete()
        
        # 创建示例车辆
        sample_vehicles = [
            {'plate': '京A12345', 'owner': '张三', 'phone': '13800138001', 'is_member': True, 'discount': 0.8},
            {'plate': '沪B67890', 'owner': '李四', 'phone': '13800138002', 'is_member': False, 'discount': 1.0},
            {'plate': '粤C11111', 'owner': '王五', 'phone': '13800138003', 'is_member': True, 'discount': 0.9},
            {'plate': '川D22222', 'owner': '赵六', 'phone': '13800138004', 'is_member': False, 'discount': 1.0},
            {'plate': '鲁E33333', 'owner': '钱七', 'phone': '13800138005', 'is_member': True, 'discount': 0.7},
            {'plate': '苏F44444', 'owner': '孙八', 'phone': '13800138006', 'is_member': False, 'discount': 1.0},
            {'plate': '浙G55555', 'owner': '周九', 'phone': '13800138007', 'is_member': True, 'discount': 0.8},
            {'plate': '闽H66666', 'owner': '吴十', 'phone': '13800138008', 'is_member': False, 'discount': 1.0},
        ]
        
        vehicles = []
        for v_data in sample_vehicles:
            vehicle = Vehicle(
                license_plate=v_data['plate'],
                owner_name=v_data['owner'],
                phone=v_data['phone'],
                is_member=v_data['is_member'],
                member_discount=v_data['discount']
            )
            vehicles.append(vehicle)
            db.session.add(vehicle)
        
        # 创建历史停车记录（最近7天）
        for i in range(50):
            vehicle = random.choice(vehicles)
            
            # 随机生成入场时间（最近7天内）
            days_ago = random.randint(0, 7)
            hours_ago = random.randint(0, 23)
            minutes_ago = random.randint(0, 59)
            
            entry_time = datetime.now() - timedelta(days=days_ago, hours=hours_ago, minutes=minutes_ago)
            
            # 随机决定是否已出场
            has_exited = random.choice([True, True, True, False])  # 75%概率已出场
            
            if has_exited:
                # 随机停车时长（1-12小时）
                parking_hours = random.uniform(1, 12)
                exit_time = entry_time + timedelta(hours=parking_hours)
                
                # 计算费用（简化计算）
                if parking_hours <= 0.25:  # 15分钟内免费
                    fee = 0
                else:
                    billable_hours = max(1, int(parking_hours))
                    fee = billable_hours * 5.0  # 5元/小时
                    if vehicle.is_member:
                        fee = fee * vehicle.member_discount
                
                is_paid = random.choice([True, True, False])  # 80%概率已支付
                payment_method = random.choice(['wechat', 'alipay', 'cash']) if is_paid else None
            else:
                exit_time = None
                fee = None
                is_paid = False
                payment_method = None
            
            record = ParkingRecord(
                license_plate=vehicle.license_plate,
                entry_time=entry_time,
                exit_time=exit_time,
                parking_fee=fee,
                is_paid=is_paid,
                payment_method=payment_method,
                entry_method=random.choice(['auto', 'manual']),
                exit_method=random.choice(['auto', 'manual']) if has_exited else None
            )
            db.session.add(record)
        
        # 创建一些当前在场的车辆
        current_vehicles = ['京A12345', '粤C11111', '鲁E33333', '浙G55555']
        for plate in current_vehicles:
            # 随机入场时间（今天内）
            hours_ago = random.randint(1, 8)
            minutes_ago = random.randint(0, 59)
            entry_time = datetime.now() - timedelta(hours=hours_ago, minutes=minutes_ago)
            
            record = ParkingRecord(
                license_plate=plate,
                entry_time=entry_time,
                exit_time=None,
                parking_fee=None,
                is_paid=False,
                payment_method=None,
                entry_method='auto'
            )
            db.session.add(record)
        
        db.session.commit()
        print(f"✓ 创建了 {len(sample_vehicles)} 个车辆信息")
        print(f"✓ 创建了 50 条历史停车记录")
        print(f"✓ 创建了 {len(current_vehicles)} 个当前在场车辆")

def show_system_info():
    """显示系统信息"""
    print("=" * 60)
    print("写字楼停车管理系统演示")
    print("=" * 60)
    print()
    print("🚗 系统功能特色：")
    print("   • 智能车牌识别（支持图片上传和摄像头识别）")
    print("   • 自动计费系统（按时长计费，支持会员优惠）")
    print("   • 灵活的价格配置（免费时间、日最高限额）")
    print("   • 多种支付方式（微信、支付宝、现金）")
    print("   • 实时数据统计和图表展示")
    print("   • 完整的管理后台功能")
    print()
    print("📊 演示数据概况：")
    print("   • 8个示例车辆（包含会员和普通用户）")
    print("   • 50条历史停车记录")
    print("   • 4辆当前在场车辆")
    print("   • 完整的收费和支付记录")
    print()
    print("🔑 登录信息：")
    print("   • 访问地址：http://localhost:5000")
    print("   • 管理员账户：admin")
    print("   • 管理员密码：admin123")
    print()
    print("💡 使用建议：")
    print("   1. 先登录管理后台查看整体数据")
    print("   2. 体验车辆识别功能（会随机返回演示车牌）")
    print("   3. 尝试手动入场/出场操作")
    print("   4. 查看统计报表和图表展示")
    print("   5. 配置价格和支付设置")
    print()

def interactive_demo():
    """交互式演示"""
    print("🎯 交互式功能演示")
    print("-" * 30)
    
    while True:
        print("\n请选择要演示的功能：")
        print("1. 模拟车辆入场")
        print("2. 模拟车辆出场")
        print("3. 查看当前在场车辆")
        print("4. 查看今日统计")
        print("5. 退出演示")
        
        choice = input("\n请输入选项 (1-5): ").strip()
        
        if choice == '1':
            simulate_vehicle_entry()
        elif choice == '2':
            simulate_vehicle_exit()
        elif choice == '3':
            show_current_vehicles()
        elif choice == '4':
            show_today_stats()
        elif choice == '5':
            print("演示结束，感谢使用！")
            break
        else:
            print("无效选项，请重新选择。")

def simulate_vehicle_entry():
    """模拟车辆入场"""
    from app_sqlite import app, db, Vehicle, ParkingRecord
    
    sample_plates = ['京A99999', '沪B88888', '粤C77777', '川D66666']
    plate = random.choice(sample_plates)
    
    with app.app_context():
        # 检查是否已在场
        existing = ParkingRecord.query.filter_by(license_plate=plate, exit_time=None).first()
        if existing:
            print(f"❌ 车辆 {plate} 已在场内")
            return
        
        # 创建入场记录
        record = ParkingRecord(
            license_plate=plate,
            entry_time=datetime.now(),
            entry_method='auto'
        )
        db.session.add(record)
        db.session.commit()
        
        print(f"✅ 车辆 {plate} 入场成功")
        print(f"   入场时间：{record.entry_time.strftime('%Y-%m-%d %H:%M:%S')}")

def simulate_vehicle_exit():
    """模拟车辆出场"""
    from app_sqlite import app, db, ParkingRecord, Vehicle, PricingConfig
    
    with app.app_context():
        # 获取在场车辆
        current_records = ParkingRecord.query.filter_by(exit_time=None).all()
        if not current_records:
            print("❌ 当前没有在场车辆")
            return
        
        record = random.choice(current_records)
        exit_time = datetime.now()
        
        # 计算费用
        duration = (exit_time - record.entry_time).total_seconds() / 3600  # 小时
        pricing = PricingConfig.query.filter_by(is_active=True).first()
        
        if duration <= (pricing.free_minutes / 60):
            fee = 0
        else:
            billable_hours = max(1, int(duration))
            fee = billable_hours * pricing.hourly_rate
            
            # 检查会员折扣
            vehicle = Vehicle.query.filter_by(license_plate=record.license_plate).first()
            if vehicle and vehicle.is_member:
                fee = fee * vehicle.member_discount
        
        # 更新记录
        record.exit_time = exit_time
        record.parking_fee = fee
        record.is_paid = True
        record.payment_method = random.choice(['wechat', 'alipay'])
        record.exit_method = 'auto'
        
        db.session.commit()
        
        print(f"✅ 车辆 {record.license_plate} 出场成功")
        print(f"   停车时长：{duration:.1f} 小时")
        print(f"   停车费用：¥{fee:.2f}")

def show_current_vehicles():
    """显示当前在场车辆"""
    from app_sqlite import app, db, ParkingRecord, Vehicle
    
    with app.app_context():
        records = ParkingRecord.query.filter_by(exit_time=None).all()
        
        if not records:
            print("📋 当前没有在场车辆")
            return
        
        print(f"📋 当前在场车辆 ({len(records)} 辆)：")
        print("-" * 50)
        
        for record in records:
            vehicle = Vehicle.query.filter_by(license_plate=record.license_plate).first()
            duration = (datetime.now() - record.entry_time).total_seconds() / 3600
            
            member_status = "会员" if vehicle and vehicle.is_member else "普通"
            print(f"🚗 {record.license_plate} | {member_status} | {duration:.1f}小时")

def show_today_stats():
    """显示今日统计"""
    from app_sqlite import app, db, ParkingRecord
    
    with app.app_context():
        today = datetime.now().date()
        today_records = ParkingRecord.query.filter(
            db.func.date(ParkingRecord.created_at) == today
        ).all()
        
        entries = len([r for r in today_records if r.entry_time])
        exits = len([r for r in today_records if r.exit_time])
        revenue = sum([r.parking_fee or 0 for r in today_records if r.is_paid])
        current_parked = ParkingRecord.query.filter_by(exit_time=None).count()
        
        print("📊 今日统计数据：")
        print("-" * 30)
        print(f"📥 今日入场：{entries} 辆")
        print(f"📤 今日出场：{exits} 辆")
        print(f"🚗 当前在场：{current_parked} 辆")
        print(f"💰 今日收入：¥{revenue:.2f}")

def main():
    """主函数"""
    print("正在启动演示程序...")
    
    # 检查应用是否可用
    try:
        from app_sqlite import app
        print("✓ 应用模块加载成功")
    except ImportError as e:
        print(f"❌ 应用模块加载失败: {e}")
        return
    
    # 显示系统信息
    show_system_info()
    
    # 询问是否创建演示数据
    create_data = input("是否创建演示数据？(y/n): ").strip().lower()
    if create_data in ['y', 'yes', '是']:
        create_demo_data()
        print("\n✅ 演示数据创建完成！")
    
    # 询问是否进行交互式演示
    interactive = input("\n是否进行交互式演示？(y/n): ").strip().lower()
    if interactive in ['y', 'yes', '是']:
        interactive_demo()
    
    print("\n🎉 演示程序结束")
    print("💡 提示：您可以继续在浏览器中体验完整功能")
    print("   访问地址：http://localhost:5000")

if __name__ == '__main__':
    main()
