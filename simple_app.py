#!/usr/bin/env python3
"""
简化版停车管理系统 - 专门测试会员功能
"""
from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime
from decimal import Decimal
from sqlalchemy import DECIMAL

app = Flask(__name__)
app.config['SECRET_KEY'] = 'test-secret-key'
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql+pymysql://root:123456@localhost/car-park'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# 数据库模型
class Admin(UserMixin, db.Model):
    __tablename__ = 'admins'
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.now)

class Vehicle(db.Model):
    __tablename__ = 'vehicles'
    id = db.Column(db.Integer, primary_key=True)
    license_plate = db.Column(db.String(20), unique=True, nullable=False)
    owner_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    is_member = db.Column(db.Boolean, default=False)
    member_discount = db.Column(DECIMAL(3, 2), default=1.0)
    created_at = db.Column(db.DateTime, default=datetime.now)

@login_manager.user_loader
def load_user(user_id):
    return Admin.query.get(int(user_id))

# 路由
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('pricing_settings'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        admin = Admin.query.filter_by(username=username).first()

        if admin and check_password_hash(admin.password_hash, password):
            login_user(admin)
            return redirect(url_for('pricing_settings'))
        else:
            flash('用户名或密码错误', 'error')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

@app.route('/pricing_settings')
@login_required
def pricing_settings():
    return render_template('pricing_settings.html')

# API路由
@app.route('/api/add_member', methods=['POST'])
@login_required
def add_member():
    try:
        data = request.get_json()
        print(f"收到会员添加请求: {data}")
        
        license_plate = data.get('license_plate', '').upper()

        if not license_plate:
            return jsonify({'success': False, 'message': '车牌号不能为空'})

        # 检查车辆是否已存在
        vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()

        if vehicle:
            # 更新现有车辆信息
            vehicle.owner_name = data.get('owner_name', '')
            vehicle.phone = data.get('phone', '')
            vehicle.is_member = True
            vehicle.member_discount = Decimal(str(data.get('member_discount', 0.9)))
            print(f"更新现有车辆: {license_plate}")
        else:
            # 创建新车辆
            vehicle = Vehicle(
                license_plate=license_plate,
                owner_name=data.get('owner_name', ''),
                phone=data.get('phone', ''),
                is_member=True,
                member_discount=Decimal(str(data.get('member_discount', 0.9)))
            )
            db.session.add(vehicle)
            print(f"创建新车辆: {license_plate}")

        db.session.commit()
        print("会员添加成功")
        return jsonify({'success': True, 'message': '会员添加成功'})
        
    except Exception as e:
        print(f"会员添加失败: {e}")
        db.session.rollback()
        return jsonify({'success': False, 'message': f'添加失败: {str(e)}'})

@app.route('/api/members')
@login_required
def get_members():
    try:
        members = Vehicle.query.filter_by(is_member=True).all()
        print(f"查询到 {len(members)} 个会员")

        result = []
        for member in members:
            result.append({
                'license_plate': member.license_plate,
                'owner_name': member.owner_name,
                'phone': member.phone,
                'member_discount': float(member.member_discount)
            })

        return jsonify({'members': result})
    except Exception as e:
        print(f"获取会员列表失败: {e}")
        return jsonify({'members': []})

@app.route('/api/remove_member', methods=['POST'])
@login_required
def remove_member():
    try:
        data = request.get_json()
        license_plate = data.get('license_plate', '').upper()

        vehicle = Vehicle.query.filter_by(license_plate=license_plate).first()
        if vehicle:
            vehicle.is_member = False
            vehicle.member_discount = Decimal('1.0')
            db.session.commit()
            return jsonify({'success': True, 'message': '会员删除成功'})
        else:
            return jsonify({'success': False, 'message': '未找到该车辆'})
    except Exception as e:
        print(f"删除会员失败: {e}")
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

if __name__ == '__main__':
    print("启动简化版停车管理系统...")
    
    # 初始化数据库
    with app.app_context():
        db.create_all()
        
        # 创建默认管理员
        if not Admin.query.filter_by(username='admin').first():
            admin = Admin(
                username='admin',
                password_hash=generate_password_hash('admin123')
            )
            db.session.add(admin)
            db.session.commit()
            print("默认管理员账户已创建: admin/admin123")
    
    print("系统启动成功!")
    print("访问地址: http://localhost:5002")
    print("管理员账户: admin / admin123")
    
    app.run(debug=True, host='0.0.0.0', port=5002)
