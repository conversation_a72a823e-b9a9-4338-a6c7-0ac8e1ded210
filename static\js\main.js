// 全局JavaScript功能

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    initializeTooltips();
    
    // 初始化表格排序
    initializeTableSorting();
    
    // 初始化自动刷新
    initializeAutoRefresh();
    
    // 初始化快捷键
    initializeKeyboardShortcuts();
});

// 初始化工具提示
function initializeTooltips() {
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// 初始化表格排序
function initializeTableSorting() {
    const tables = document.querySelectorAll('.sortable-table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th[data-sort]');
        headers.forEach(header => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', function() {
                sortTable(table, this.dataset.sort);
            });
        });
    });
}

// 表格排序功能
function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = table.dataset.sortOrder !== 'asc';
    
    rows.sort((a, b) => {
        const aValue = a.querySelector(`td[data-${column}]`).textContent.trim();
        const bValue = b.querySelector(`td[data-${column}]`).textContent.trim();
        
        if (isNumeric(aValue) && isNumeric(bValue)) {
            return isAscending ? 
                parseFloat(aValue) - parseFloat(bValue) : 
                parseFloat(bValue) - parseFloat(aValue);
        } else {
            return isAscending ? 
                aValue.localeCompare(bValue) : 
                bValue.localeCompare(aValue);
        }
    });
    
    tbody.innerHTML = '';
    rows.forEach(row => tbody.appendChild(row));
    
    table.dataset.sortOrder = isAscending ? 'asc' : 'desc';
}

// 检查是否为数字
function isNumeric(str) {
    return !isNaN(str) && !isNaN(parseFloat(str));
}

// 初始化自动刷新
function initializeAutoRefresh() {
    // 每30秒刷新一次仪表板数据
    if (window.location.pathname === '/dashboard') {
        setInterval(refreshDashboardData, 30000);
    }
}

// 刷新仪表板数据
function refreshDashboardData() {
    fetch('/api/dashboard_stats')
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            updateDashboardStats(data.stats);
        }
    })
    .catch(error => {
        console.error('刷新数据失败:', error);
    });
}

// 更新仪表板统计数据
function updateDashboardStats(stats) {
    const elements = {
        'today_entries': document.querySelector('#todayEntries'),
        'today_exits': document.querySelector('#todayExits'),
        'current_parked': document.querySelector('#currentParked'),
        'today_revenue': document.querySelector('#todayRevenue')
    };
    
    Object.keys(elements).forEach(key => {
        if (elements[key]) {
            if (key === 'today_revenue') {
                elements[key].textContent = `¥${stats[key].toFixed(2)}`;
            } else {
                elements[key].textContent = stats[key];
            }
        }
    });
}

// 初始化快捷键
function initializeKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        // Ctrl + Enter: 快速入场
        if (e.ctrlKey && e.key === 'Enter') {
            e.preventDefault();
            if (typeof manualEntry === 'function') {
                manualEntry();
            }
        }
        
        // Ctrl + Shift + Enter: 快速出场
        if (e.ctrlKey && e.shiftKey && e.key === 'Enter') {
            e.preventDefault();
            if (typeof manualExit === 'function') {
                manualExit();
            }
        }
        
        // F5: 刷新当前页面数据
        if (e.key === 'F5') {
            e.preventDefault();
            location.reload();
        }
    });
}

// 通用AJAX请求函数
function makeRequest(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
        }
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    return fetch(url, finalOptions)
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    });
}

// 显示加载状态
function showLoading(element) {
    if (element) {
        element.innerHTML = '<div class="loading"></div> 加载中...';
        element.disabled = true;
    }
}

// 隐藏加载状态
function hideLoading(element, originalText) {
    if (element) {
        element.innerHTML = originalText;
        element.disabled = false;
    }
}

// 显示成功消息
function showSuccess(message) {
    showAlert(message, 'success');
}

// 显示错误消息
function showError(message) {
    showAlert(message, 'danger');
}

// 显示警告消息
function showWarning(message) {
    showAlert(message, 'warning');
}

// 显示信息消息
function showInfo(message) {
    showAlert(message, 'info');
}

// 通用警告框显示函数
function showAlert(message, type = 'info') {
    const alertContainer = document.querySelector('.container-fluid');
    if (!alertContainer) return;
    
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    // 插入到容器顶部
    alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
    
    // 5秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// 格式化日期时间
function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 格式化金额
function formatCurrency(amount) {
    return `¥${parseFloat(amount).toFixed(2)}`;
}

// 计算停车时长
function calculateDuration(entryTime, exitTime) {
    const entry = new Date(entryTime);
    const exit = exitTime ? new Date(exitTime) : new Date();
    const duration = exit - entry;
    
    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}小时${minutes}分钟`;
}

// 验证车牌号格式
function validateLicensePlate(plate) {
    const pattern = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z][A-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]$/;
    return pattern.test(plate);
}

// 复制到剪贴板
function copyToClipboard(text) {
    if (navigator.clipboard) {
        navigator.clipboard.writeText(text).then(() => {
            showSuccess('已复制到剪贴板');
        }).catch(() => {
            showError('复制失败');
        });
    } else {
        // 兼容旧浏览器
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        try {
            document.execCommand('copy');
            showSuccess('已复制到剪贴板');
        } catch (err) {
            showError('复制失败');
        }
        document.body.removeChild(textArea);
    }
}

// 导出表格数据为CSV
function exportTableToCSV(tableId, filename = 'data.csv') {
    const table = document.getElementById(tableId);
    if (!table) return;
    
    const rows = table.querySelectorAll('tr');
    const csvContent = [];
    
    rows.forEach(row => {
        const cols = row.querySelectorAll('td, th');
        const rowData = Array.from(cols).map(col => {
            return '"' + col.textContent.replace(/"/g, '""') + '"';
        });
        csvContent.push(rowData.join(','));
    });
    
    const csvString = csvContent.join('\n');
    const blob = new Blob(['\ufeff' + csvString], { type: 'text/csv;charset=utf-8;' });
    
    const link = document.createElement('a');
    if (link.download !== undefined) {
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// 打印页面
function printPage() {
    window.print();
}

// 全屏切换
function toggleFullscreen() {
    if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
    } else {
        if (document.exitFullscreen) {
            document.exitFullscreen();
        }
    }
}

// 检查网络连接状态
function checkNetworkStatus() {
    if (navigator.onLine) {
        showSuccess('网络连接正常');
    } else {
        showError('网络连接断开');
    }
}

// 监听网络状态变化
window.addEventListener('online', () => {
    showSuccess('网络连接已恢复');
});

window.addEventListener('offline', () => {
    showError('网络连接已断开');
});

// 防抖函数
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// 节流函数
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}
