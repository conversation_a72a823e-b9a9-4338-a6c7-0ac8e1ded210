{% extends "base.html" %}

{% block title %}车辆识别 - 写字楼停车管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-camera"></i> 车辆识别</h2>
        <hr>
    </div>
</div>

<div class="row">
    <!-- 图片上传识别 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-upload"></i> 图片上传识别</h5>
            </div>
            <div class="card-body">
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="imageFile" class="form-label">选择车辆图片</label>
                        <input type="file" class="form-control" id="imageFile" accept="image/*" required>
                    </div>
                    <div class="mb-3">
                        <img id="previewImage" src="" alt="预览图片" style="max-width: 100%; height: auto; display: none;">
                    </div>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i> 识别车牌
                    </button>
                </form>
                
                <div id="uploadResult" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <strong>识别结果：</strong>
                        <span id="uploadLicensePlate"></span>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="processEntry('upload')">
                            <i class="fas fa-plus"></i> 确认入场
                        </button>
                        <button class="btn btn-warning" onclick="processExit('upload')">
                            <i class="fas fa-minus"></i> 确认出场
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 摄像头实时识别 -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-video"></i> 摄像头实时识别</h5>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <video id="cameraVideo" width="100%" height="300" style="border: 1px solid #ddd; display: none;"></video>
                    <canvas id="cameraCanvas" width="100%" height="300" style="border: 1px solid #ddd; display: none;"></canvas>
                    <div id="cameraPlaceholder" class="text-center p-5" style="border: 1px solid #ddd; background-color: #f8f9fa;">
                        <i class="fas fa-camera fa-3x text-muted"></i>
                        <p class="mt-2 text-muted">点击下方按钮启动摄像头</p>
                    </div>
                </div>
                
                <div class="d-grid gap-2">
                    <button id="startCameraBtn" class="btn btn-primary" onclick="startCamera()">
                        <i class="fas fa-play"></i> 启动摄像头
                    </button>
                    <button id="captureBtn" class="btn btn-success" onclick="captureImage()" style="display: none;">
                        <i class="fas fa-camera"></i> 拍照识别
                    </button>
                    <button id="stopCameraBtn" class="btn btn-danger" onclick="stopCamera()" style="display: none;">
                        <i class="fas fa-stop"></i> 停止摄像头
                    </button>
                </div>
                
                <div id="cameraResult" class="mt-3" style="display: none;">
                    <div class="alert alert-info">
                        <strong>识别结果：</strong>
                        <span id="cameraLicensePlate"></span>
                    </div>
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="processEntry('camera')">
                            <i class="fas fa-plus"></i> 确认入场
                        </button>
                        <button class="btn btn-warning" onclick="processExit('camera')">
                            <i class="fas fa-minus"></i> 确认出场
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 处理结果模态框 -->
<div class="modal fade" id="processModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="processModalTitle">处理结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="processModalContent"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" id="paymentBtn" class="btn btn-primary" style="display: none;" onclick="openPayment()">
                    前往支付
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentStream = null;
let currentLicensePlate = '';
let currentRecordId = null;

// 图片上传预览
document.getElementById('imageFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('previewImage');
            preview.src = e.target.result;
            preview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
});

// 图片上传识别
document.getElementById('uploadForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData();
    const fileInput = document.getElementById('imageFile');
    
    if (!fileInput.files[0]) {
        alert('请选择图片文件');
        return;
    }
    
    formData.append('image', fileInput.files[0]);
    
    fetch('/api/upload_image', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('uploadLicensePlate').textContent = data.license_plate;
            document.getElementById('uploadResult').style.display = 'block';
            currentLicensePlate = data.license_plate;
        } else {
            alert('识别失败：' + data.message);
        }
    })
    .catch(error => {
        alert('识别过程中出错：' + error.message);
    });
});

// 启动摄像头
function startCamera() {
    navigator.mediaDevices.getUserMedia({ video: true })
    .then(function(stream) {
        currentStream = stream;
        const video = document.getElementById('cameraVideo');
        video.srcObject = stream;
        video.play();
        
        document.getElementById('cameraPlaceholder').style.display = 'none';
        document.getElementById('cameraVideo').style.display = 'block';
        document.getElementById('startCameraBtn').style.display = 'none';
        document.getElementById('captureBtn').style.display = 'block';
        document.getElementById('stopCameraBtn').style.display = 'block';
    })
    .catch(function(err) {
        alert('无法访问摄像头：' + err.message);
    });
}

// 停止摄像头
function stopCamera() {
    if (currentStream) {
        currentStream.getTracks().forEach(track => track.stop());
        currentStream = null;
    }
    
    document.getElementById('cameraVideo').style.display = 'none';
    document.getElementById('cameraCanvas').style.display = 'none';
    document.getElementById('cameraPlaceholder').style.display = 'block';
    document.getElementById('startCameraBtn').style.display = 'block';
    document.getElementById('captureBtn').style.display = 'none';
    document.getElementById('stopCameraBtn').style.display = 'none';
    document.getElementById('cameraResult').style.display = 'none';
}

// 拍照识别
function captureImage() {
    const video = document.getElementById('cameraVideo');
    const canvas = document.getElementById('cameraCanvas');
    const context = canvas.getContext('2d');
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0);
    
    // 发送到后端识别
    fetch('/api/camera_capture', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('cameraLicensePlate').textContent = data.license_plate;
            document.getElementById('cameraResult').style.display = 'block';
            currentLicensePlate = data.license_plate;
        } else {
            alert('识别失败：' + data.message);
        }
    })
    .catch(error => {
        alert('识别过程中出错：' + error.message);
    });
}

// 处理入场
function processEntry(source) {
    if (!currentLicensePlate) {
        alert('请先识别车牌');
        return;
    }
    
    fetch('/api/manual_entry', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            license_plate: currentLicensePlate,
            entry_method: 'auto'
        })
    })
    .then(response => response.json())
    .then(data => {
        showProcessResult('入场处理', data.message, data.success);
    });
}

// 处理出场
function processExit(source) {
    if (!currentLicensePlate) {
        alert('请先识别车牌');
        return;
    }
    
    fetch('/api/manual_exit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            license_plate: currentLicensePlate
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.fee > 0) {
            currentRecordId = data.record_id;
            showProcessResult('出场处理', `出场成功！停车费用：¥${data.fee}`, true, true);
        } else {
            showProcessResult('出场处理', data.message, data.success);
        }
    });
}

// 显示处理结果
function showProcessResult(title, message, success, showPayment = false) {
    document.getElementById('processModalTitle').textContent = title;
    document.getElementById('processModalContent').innerHTML = `
        <div class="alert alert-${success ? 'success' : 'danger'}">
            ${message}
        </div>
    `;
    
    if (showPayment) {
        document.getElementById('paymentBtn').style.display = 'block';
    } else {
        document.getElementById('paymentBtn').style.display = 'none';
    }
    
    $('#processModal').modal('show');
}

// 打开支付页面
function openPayment() {
    if (currentRecordId) {
        window.open('/payment/' + currentRecordId, '_blank');
    }
}
</script>
{% endblock %}
