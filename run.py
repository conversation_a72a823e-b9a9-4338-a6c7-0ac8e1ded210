#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
写字楼停车管理系统启动脚本
生产环境版本
"""

import os
import sys
import pymysql

def create_database_if_not_exists():
    """创建数据库（如果不存在）"""
    try:
        # 连接MySQL服务器（不指定数据库）
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )

        cursor = connection.cursor()

        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE 'car-park'")
        result = cursor.fetchone()

        if not result:
            # 创建数据库
            cursor.execute("CREATE DATABASE `car-park` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✓ 数据库 'car-park' 创建成功!")
        else:
            print("✓ 数据库 'car-park' 已存在")

        cursor.close()
        connection.close()
        return True

    except Exception as e:
        print(f"❌ 数据库操作失败: {e}")
        print("请确保:")
        print("1. MySQL服务已启动")
        print("2. 用户名密码正确 (root/123456)")
        print("3. 已安装PyMySQL: pip install pymysql")
        return False

def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'flask': 'flask',
        'flask_sqlalchemy': 'flask_sqlalchemy',
        'flask_login': 'flask_login',
        'pymysql': 'pymysql',
        'werkzeug': 'werkzeug',
        'opencv-python': 'cv2',
        'pillow': 'PIL',
        'numpy': 'numpy'
    }

    # 单独检查PaddleOCR相关包
    optional_packages = {
        'paddlepaddle': 'paddle',
        'paddleocr': 'paddleocr'
    }

    missing_packages = []

    # 检查必需包
    for package_name, import_name in required_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)

    # 检查可选包（OCR库）- 使用安全的检查方式
    ocr_available = False
    import importlib.util

    # 检查EasyOCR
    try:
        spec = importlib.util.find_spec("easyocr")
        if spec is not None:
            ocr_available = True
            print("✓ EasyOCR已安装，将使用车牌识别功能")
    except Exception:
        pass

    # 检查PaddleOCR（如果EasyOCR不可用）
    if not ocr_available:
        try:
            spec = importlib.util.find_spec("paddleocr")
            if spec is not None:
                ocr_available = True
                print("✓ PaddleOCR已安装，将尝试使用车牌识别功能")
        except Exception:
            pass

    if not ocr_available:
        print("⚠️ 未安装OCR库，将使用图像分析和手动输入模式")
        print("💡 如需自动识别功能，可安装：pip install easyocr")

    if missing_packages:
        print("❌ 缺少以下必需依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False

    return True

def main():
    """主函数"""
    print("=" * 60)
    print("写字楼停车管理系统")
    print("=" * 60)

    # 检查依赖包
    print("1. 检查依赖包...")
    if not check_dependencies():
        return False
    print("   ✓ 依赖包检查通过")

    # 创建数据库
    print("\n2. 检查数据库连接...")
    if not create_database_if_not_exists():
        return False
    print("   ✓ 数据库连接检查通过")

    # 创建必要目录
    print("\n3. 创建必要目录...")
    directories = [
        'static/uploads',
        'static/qrcodes',
        'static/css',
        'static/js'
    ]

    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   创建目录: {directory}")
    print("   ✓ 目录创建完成")

    print("\n" + "=" * 60)
    print("系统检查完成，正在启动应用...")
    print("=" * 60)

    # 启动应用
    try:
        from app import app

        # 初始化数据库
        with app.app_context():
            from app import db, Admin, PricingConfig
            from werkzeug.security import generate_password_hash
            from decimal import Decimal

            # 创建表
            db.create_all()
            print("✓ 数据库表创建成功!")

            # 创建默认管理员账户
            if not Admin.query.filter_by(username='admin').first():
                admin = Admin(
                    username='admin',
                    password_hash=generate_password_hash('admin123')
                )
                db.session.add(admin)
                print("✓ 默认管理员账户已创建: admin/admin123")

            # 创建默认价格配置
            if not PricingConfig.query.filter_by(is_active=True).first():
                default_pricing = PricingConfig(
                    config_name='默认价格配置',
                    hourly_rate=Decimal('5.00'),
                    daily_max=Decimal('50.00'),
                    free_minutes=15,
                    is_active=True
                )
                db.session.add(default_pricing)
                print("✓ 默认价格配置已创建: 5元/小时，日最高50元，免费15分钟")

            db.session.commit()
            print("✓ 数据初始化完成!")

        print("\n" + "=" * 60)
        print("🎉 系统启动成功!")
        print("📱 访问地址: http://localhost:5000")
        print("👤 管理员账户: admin")
        print("🔑 管理员密码: admin123")
        print("⚠️  按 Ctrl+C 停止服务")
        print("=" * 60)

        # 启动Flask应用
        app.run(debug=False, host='0.0.0.0', port=5000)

    except KeyboardInterrupt:
        print("\n\n✓ 系统已安全停止")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        print("请检查:")
        print("1. 数据库连接是否正常")
        print("2. 依赖包是否完整安装")
        print("3. 端口5000是否被占用")
        return False

    return True

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n启动失败，请检查上述错误信息")
        input("按回车键退出...")
        sys.exit(1)
