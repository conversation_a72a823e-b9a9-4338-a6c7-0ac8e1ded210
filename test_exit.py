#!/usr/bin/env python3
"""
测试车辆出场功能
"""
import requests
import json

# 测试配置
BASE_URL = "http://localhost:5000"
TEST_LICENSE_PLATE = "京A12345"

def test_manual_entry():
    """测试手动入场"""
    print("=== 测试手动入场 ===")
    
    url = f"{BASE_URL}/api/manual_entry"
    data = {
        "license_plate": TEST_LICENSE_PLATE,
        "entry_method": "manual"
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    print(f"入场结果: {result}")
    return result.get('success', False)

def test_manual_exit():
    """测试手动出场"""
    print("=== 测试手动出场 ===")
    
    url = f"{BASE_URL}/api/manual_exit"
    data = {
        "license_plate": TEST_LICENSE_PLATE
    }
    
    response = requests.post(url, json=data)
    result = response.json()
    
    print(f"出场结果: {result}")
    
    if result.get('success') and result.get('fee', 0) > 0:
        print(f"✅ 出场成功！费用: ¥{result['fee']}")
        print(f"✅ 记录ID: {result['record_id']}")
        return True, result['record_id']
    else:
        print(f"❌ 出场失败: {result.get('message', '未知错误')}")
        return False, None

def test_payment_page(record_id):
    """测试支付页面"""
    print("=== 测试支付页面 ===")
    
    url = f"{BASE_URL}/payment/{record_id}"
    response = requests.get(url)
    
    if response.status_code == 200:
        print(f"✅ 支付页面可访问: {url}")
        return True
    else:
        print(f"❌ 支付页面访问失败: {response.status_code}")
        return False

def main():
    print("开始测试车辆出场和支付功能...")
    
    # 1. 先测试入场
    if test_manual_entry():
        print("✅ 入场成功")
        
        # 2. 测试出场
        success, record_id = test_manual_exit()
        
        if success and record_id:
            print("✅ 出场成功")
            
            # 3. 测试支付页面
            if test_payment_page(record_id):
                print("✅ 支付页面正常")
                print("\n🎉 所有测试通过！出场和支付功能正常工作")
            else:
                print("❌ 支付页面测试失败")
        else:
            print("❌ 出场测试失败")
    else:
        print("❌ 入场测试失败")

if __name__ == "__main__":
    main()
