#!/usr/bin/env python3
"""
测试数据库连接
"""
import pymysql

try:
    # 测试MySQL连接
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='123456',
        database='car-park',
        charset='utf8mb4'
    )
    
    print("✅ MySQL数据库连接成功!")
    
    # 测试查询
    with connection.cursor() as cursor:
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"✅ 数据库中的表: {tables}")
        
        # 测试会员表
        cursor.execute("SELECT COUNT(*) FROM vehicles WHERE is_member = 1")
        member_count = cursor.fetchone()[0]
        print(f"✅ 当前会员数量: {member_count}")
        
    connection.close()
    print("✅ 数据库测试完成")
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")
