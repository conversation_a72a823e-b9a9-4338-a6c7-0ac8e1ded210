#!/usr/bin/env python3
"""
测试Flask应用启动
"""
from flask import Flask, jsonify
import pymysql

app = Flask(__name__)

@app.route('/')
def test():
    return "Flask应用正常运行!"

@app.route('/test_db')
def test_db():
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='car-park',
            charset='utf8mb4'
        )
        connection.close()
        return jsonify({"status": "success", "message": "数据库连接正常"})
    except Exception as e:
        return jsonify({"status": "error", "message": str(e)})

if __name__ == '__main__':
    print("启动测试Flask应用...")
    app.run(debug=True, host='0.0.0.0', port=5001)
