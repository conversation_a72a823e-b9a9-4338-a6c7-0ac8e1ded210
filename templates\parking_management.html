{% extends "base.html" %}

{% block title %}停车管理 - 写字楼停车管理系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h2><i class="fas fa-parking"></i> 停车管理</h2>
        <hr>
    </div>
</div>

<!-- 当前在场车辆概览 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="totalParked">{{ vehicles|length }}</h4>
                        <p class="mb-0">当前在场</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-car fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="memberCount">0</h4>
                        <p class="mb-0">会员车辆</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-star fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="longParked">0</h4>
                        <p class="mb-0">超时停车</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4 id="estimatedRevenue">¥0.00</h4>
                        <p class="mb-0">预计收入</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索和筛选 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form class="row g-3" id="searchForm">
                    <div class="col-md-4">
                        <label for="searchPlate" class="form-label">车牌号搜索</label>
                        <input type="text" class="form-control" id="searchPlate" placeholder="输入车牌号">
                    </div>
                    <div class="col-md-3">
                        <label for="filterType" class="form-label">车辆类型</label>
                        <select class="form-control" id="filterType">
                            <option value="">全部</option>
                            <option value="member">会员车辆</option>
                            <option value="normal">普通车辆</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="sortBy" class="form-label">排序方式</label>
                        <select class="form-control" id="sortBy">
                            <option value="entry_time">入场时间</option>
                            <option value="duration">停车时长</option>
                            <option value="estimated_fee">预计费用</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> 搜索
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 在场车辆列表 -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="fas fa-list"></i> 在场车辆列表</h5>
                <div>
                    <button class="btn btn-success btn-sm" onclick="refreshData()">
                        <i class="fas fa-sync-alt"></i> 刷新
                    </button>
                    <button class="btn btn-info btn-sm" onclick="exportData()">
                        <i class="fas fa-download"></i> 导出
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped sortable-table" id="vehiclesTable">
                        <thead>
                            <tr>
                                <th data-sort="license_plate">车牌号</th>
                                <th data-sort="owner_name">车主姓名</th>
                                <th data-sort="entry_time">入场时间</th>
                                <th data-sort="duration">停车时长</th>
                                <th data-sort="estimated_fee">预计费用</th>
                                <th>车辆类型</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="vehiclesTableBody">
                            <!-- 数据将通过JavaScript动态加载 -->
                        </tbody>
                    </table>
                </div>
                
                <!-- 分页 -->
                <nav aria-label="分页导航">
                    <ul class="pagination justify-content-center" id="pagination">
                        <!-- 分页按钮将动态生成 -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- 车辆详情模态框 -->
<div class="modal fade" id="vehicleDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">车辆详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="vehicleDetailContent">
                    <!-- 详情内容将动态加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-warning" id="exitVehicleBtn">
                    <i class="fas fa-sign-out-alt"></i> 办理出场
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作模态框 -->
<div class="modal fade" id="batchOperationModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">批量操作</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>选择要执行的批量操作：</p>
                <div class="d-grid gap-2">
                    <button class="btn btn-warning" onclick="batchExit()">
                        <i class="fas fa-sign-out-alt"></i> 批量出场
                    </button>
                    <button class="btn btn-info" onclick="batchExport()">
                        <i class="fas fa-download"></i> 导出选中车辆
                    </button>
                </div>
                <div class="mt-3">
                    <small class="text-muted">已选择 <span id="selectedCount">0</span> 辆车</small>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let currentPage = 1;
const pageSize = 20;
let selectedVehicles = new Set();
let allVehicles = [];

// 页面加载时初始化
document.addEventListener('DOMContentLoaded', function() {
    loadVehicles();
    
    // 搜索表单提交
    document.getElementById('searchForm').addEventListener('submit', function(e) {
        e.preventDefault();
        currentPage = 1;
        loadVehicles();
    });
    
    // 实时搜索
    document.getElementById('searchPlate').addEventListener('input', debounce(function() {
        currentPage = 1;
        loadVehicles();
    }, 500));
    
    // 筛选和排序变化
    ['filterType', 'sortBy'].forEach(id => {
        document.getElementById(id).addEventListener('change', function() {
            currentPage = 1;
            loadVehicles();
        });
    });
    
    // 定时刷新数据
    setInterval(loadVehicles, 60000); // 每分钟刷新一次
});

// 加载车辆数据
function loadVehicles() {
    const searchPlate = document.getElementById('searchPlate').value;
    const filterType = document.getElementById('filterType').value;
    const sortBy = document.getElementById('sortBy').value;
    
    const params = new URLSearchParams({
        page: currentPage,
        page_size: pageSize,
        search: searchPlate,
        filter_type: filterType,
        sort_by: sortBy
    });
    
    fetch('/api/parked_vehicles?' + params)
    .then(response => response.json())
    .then(data => {
        allVehicles = data.vehicles;
        updateVehiclesTable(data.vehicles);
        updatePagination(data.total_pages, currentPage);
        updateStatistics(data.statistics);
    })
    .catch(error => {
        console.error('加载车辆数据失败:', error);
        showError('加载数据失败');
    });
}

// 更新车辆表格
function updateVehiclesTable(vehicles) {
    const tbody = document.getElementById('vehiclesTableBody');
    tbody.innerHTML = '';
    
    vehicles.forEach(vehicle => {
        const row = tbody.insertRow();
        row.innerHTML = `
            <td>
                <input type="checkbox" class="form-check-input me-2" 
                       onchange="toggleVehicleSelection('${vehicle.license_plate}')">
                <span class="license-plate">${vehicle.license_plate}</span>
            </td>
            <td>${vehicle.owner_name || '未设置'}</td>
            <td>${formatDateTime(vehicle.entry_time)}</td>
            <td>${vehicle.duration}</td>
            <td class="amount">¥${vehicle.estimated_fee}</td>
            <td>
                <span class="badge bg-${vehicle.is_member ? 'success' : 'secondary'}">
                    ${vehicle.is_member ? '会员' : '普通'}
                </span>
            </td>
            <td>
                <button class="btn btn-sm btn-info" onclick="showVehicleDetail('${vehicle.license_plate}')">
                    <i class="fas fa-eye"></i> 详情
                </button>
                <button class="btn btn-sm btn-warning" onclick="exitVehicle('${vehicle.license_plate}')">
                    <i class="fas fa-sign-out-alt"></i> 出场
                </button>
            </td>
        `;
    });
}

// 更新统计数据
function updateStatistics(stats) {
    document.getElementById('totalParked').textContent = stats.total_parked;
    document.getElementById('memberCount').textContent = stats.member_count;
    document.getElementById('longParked').textContent = stats.long_parked;
    document.getElementById('estimatedRevenue').textContent = formatCurrency(stats.estimated_revenue);
}

// 更新分页
function updatePagination(totalPages, currentPage) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // 上一页
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">上一页</a>`;
    pagination.appendChild(prevLi);
    
    // 页码
    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // 下一页
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">下一页</a>`;
    pagination.appendChild(nextLi);
}

// 切换页面
function changePage(page) {
    if (page >= 1) {
        currentPage = page;
        loadVehicles();
    }
}

// 显示车辆详情
function showVehicleDetail(licensePlate) {
    fetch(`/api/vehicle_detail/${licensePlate}`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const content = document.getElementById('vehicleDetailContent');
            content.innerHTML = `
                <div class="row">
                    <div class="col-md-6">
                        <h6>基本信息</h6>
                        <table class="table table-sm">
                            <tr><td>车牌号：</td><td class="license-plate">${data.vehicle.license_plate}</td></tr>
                            <tr><td>车主姓名：</td><td>${data.vehicle.owner_name || '未设置'}</td></tr>
                            <tr><td>联系电话：</td><td>${data.vehicle.phone || '未设置'}</td></tr>
                            <tr><td>车辆类型：</td><td>
                                <span class="badge bg-${data.vehicle.is_member ? 'success' : 'secondary'}">
                                    ${data.vehicle.is_member ? '会员' : '普通'}
                                </span>
                            </td></tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>停车信息</h6>
                        <table class="table table-sm">
                            <tr><td>入场时间：</td><td>${formatDateTime(data.parking.entry_time)}</td></tr>
                            <tr><td>停车时长：</td><td>${data.parking.duration}</td></tr>
                            <tr><td>预计费用：</td><td class="amount">¥${data.parking.estimated_fee}</td></tr>
                            <tr><td>入场方式：</td><td>${data.parking.entry_method === 'auto' ? '自动识别' : '手动录入'}</td></tr>
                        </table>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>历史记录</h6>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>日期</th>
                                        <th>入场时间</th>
                                        <th>出场时间</th>
                                        <th>停车费用</th>
                                        <th>支付状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.history.map(record => `
                                        <tr>
                                            <td>${record.date}</td>
                                            <td>${record.entry_time}</td>
                                            <td>${record.exit_time || '未出场'}</td>
                                            <td>¥${record.parking_fee || '0.00'}</td>
                                            <td>
                                                <span class="badge bg-${record.is_paid ? 'success' : 'warning'}">
                                                    ${record.is_paid ? '已支付' : '未支付'}
                                                </span>
                                            </td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            `;
            
            // 设置出场按钮事件
            document.getElementById('exitVehicleBtn').onclick = () => {
                exitVehicle(licensePlate);
                $('#vehicleDetailModal').modal('hide');
            };
            
            $('#vehicleDetailModal').modal('show');
        } else {
            showError('获取车辆详情失败：' + data.message);
        }
    })
    .catch(error => {
        console.error('获取车辆详情失败:', error);
        showError('获取车辆详情失败');
    });
}

// 车辆出场
function exitVehicle(licensePlate) {
    if (confirm(`确定要为车辆 ${licensePlate} 办理出场吗？`)) {
        fetch('/api/manual_exit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({license_plate: licensePlate})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess('出场成功！');
                if (data.fee > 0) {
                    window.open('/payment/' + data.record_id, '_blank');
                }
                loadVehicles(); // 刷新列表
            } else {
                showError('出场失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('出场操作失败:', error);
            showError('出场操作失败');
        });
    }
}

// 切换车辆选择
function toggleVehicleSelection(licensePlate) {
    if (selectedVehicles.has(licensePlate)) {
        selectedVehicles.delete(licensePlate);
    } else {
        selectedVehicles.add(licensePlate);
    }
    
    document.getElementById('selectedCount').textContent = selectedVehicles.size;
    
    // 显示/隐藏批量操作按钮
    const batchBtn = document.getElementById('batchOperationBtn');
    if (selectedVehicles.size > 0 && !batchBtn) {
        const cardHeader = document.querySelector('.card-header');
        const newBtn = document.createElement('button');
        newBtn.id = 'batchOperationBtn';
        newBtn.className = 'btn btn-warning btn-sm ms-2';
        newBtn.innerHTML = '<i class="fas fa-tasks"></i> 批量操作';
        newBtn.onclick = () => $('#batchOperationModal').modal('show');
        cardHeader.querySelector('div').appendChild(newBtn);
    } else if (selectedVehicles.size === 0 && batchBtn) {
        batchBtn.remove();
    }
}

// 刷新数据
function refreshData() {
    loadVehicles();
    showSuccess('数据已刷新');
}

// 导出数据
function exportData() {
    exportTableToCSV('vehiclesTable', '在场车辆列表.csv');
}

// 批量出场
function batchExit() {
    if (selectedVehicles.size === 0) {
        showWarning('请先选择要出场的车辆');
        return;
    }
    
    if (confirm(`确定要为选中的 ${selectedVehicles.size} 辆车办理出场吗？`)) {
        const promises = Array.from(selectedVehicles).map(licensePlate => {
            return fetch('/api/manual_exit', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({license_plate: licensePlate})
            }).then(response => response.json());
        });
        
        Promise.all(promises)
        .then(results => {
            const successful = results.filter(r => r.success).length;
            const failed = results.length - successful;
            
            if (failed === 0) {
                showSuccess(`批量出场成功！共处理 ${successful} 辆车`);
            } else {
                showWarning(`批量出场完成！成功 ${successful} 辆，失败 ${failed} 辆`);
            }
            
            selectedVehicles.clear();
            $('#batchOperationModal').modal('hide');
            loadVehicles();
        })
        .catch(error => {
            console.error('批量出场失败:', error);
            showError('批量出场失败');
        });
    }
}

// 批量导出
function batchExport() {
    if (selectedVehicles.size === 0) {
        showWarning('请先选择要导出的车辆');
        return;
    }
    
    const selectedData = allVehicles.filter(v => selectedVehicles.has(v.license_plate));
    
    // 创建CSV内容
    const headers = ['车牌号', '车主姓名', '入场时间', '停车时长', '预计费用', '车辆类型'];
    const csvContent = [
        headers.join(','),
        ...selectedData.map(v => [
            v.license_plate,
            v.owner_name || '未设置',
            v.entry_time,
            v.duration,
            v.estimated_fee,
            v.is_member ? '会员' : '普通'
        ].join(','))
    ].join('\n');
    
    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '选中车辆列表.csv';
    link.click();
    
    showSuccess('导出成功');
    $('#batchOperationModal').modal('hide');
}
</script>
{% endblock %}
