#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的启动脚本 - 避免依赖检查问题
"""

import os
import sys

def main():
    """主函数"""
    print("=" * 60)
    print("🚗 写字楼停车管理系统")
    print("=" * 60)
    
    print("🔧 正在启动系统...")
    
    # 创建必要目录
    directories = [
        'static/uploads',
        'static/qrcodes',
        'static/css',
        'static/js'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   📁 创建目录: {directory}")
    
    print("✅ 目录检查完成")
    
    # 启动应用
    try:
        print("🚀 正在启动Flask应用...")
        from app import app
        
        print("\n" + "=" * 60)
        print("🎉 系统启动成功!")
        print("📱 访问地址: http://localhost:5000")
        print("👤 管理员账户: admin")
        print("🔑 管理员密码: admin123")
        print("⚠️  按 Ctrl+C 停止服务")
        print("=" * 60)
        
        # 启动Flask应用
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n✅ 系统已安全停止")
    except Exception as e:
        print(f"\n❌ 系统启动失败: {e}")
        print("💡 建议:")
        print("   1. 检查MySQL数据库是否启动")
        print("   2. 检查端口5000是否被占用")
        print("   3. 检查依赖包是否安装完整")
        return False
    
    return True

if __name__ == '__main__':
    try:
        success = main()
        if not success:
            input("\n按回车键退出...")
            sys.exit(1)
    except Exception as e:
        print(f"启动脚本出错: {e}")
        input("按回车键退出...")
        sys.exit(1)
