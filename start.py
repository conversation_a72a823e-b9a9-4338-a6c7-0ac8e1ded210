#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
写字楼停车管理系统启动脚本
"""

import os
import sys
import subprocess
import time

def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("错误: 需要Python 3.8或更高版本")
        print(f"当前版本: {sys.version}")
        return False
    return True

def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'flask',
        'flask-sqlalchemy',
        'flask-login',
        'pymysql',
        'opencv-python',
        'paddlepaddle',
        'paddleocr',
        'pillow',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_mysql_connection():
    """检查MySQL连接"""
    try:
        import pymysql
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            charset='utf8mb4'
        )
        
        # 检查数据库是否存在
        cursor = connection.cursor()
        cursor.execute("SHOW DATABASES LIKE 'car-park'")
        result = cursor.fetchone()
        
        if not result:
            print("数据库 'car-park' 不存在，正在创建...")
            cursor.execute("CREATE DATABASE `car-park` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("数据库创建成功!")
        
        cursor.close()
        connection.close()
        return True
        
    except Exception as e:
        print(f"MySQL连接失败: {e}")
        print("请确保:")
        print("1. MySQL服务已启动")
        print("2. 用户名密码正确 (root/123456)")
        print("3. 已安装PyMySQL: pip install pymysql")
        return False

def create_directories():
    """创建必要的目录"""
    directories = [
        'static/uploads',
        'static/qrcodes',
        'static/css',
        'static/js'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"创建目录: {directory}")

def main():
    """主函数"""
    print("=" * 50)
    print("写字楼停车管理系统启动检查")
    print("=" * 50)
    
    # 检查Python版本
    print("1. 检查Python版本...")
    if not check_python_version():
        return False
    print("   ✓ Python版本检查通过")
    
    # 检查依赖包
    print("\n2. 检查依赖包...")
    if not check_dependencies():
        return False
    print("   ✓ 依赖包检查通过")
    
    # 检查MySQL连接
    print("\n3. 检查MySQL连接...")
    if not check_mysql_connection():
        return False
    print("   ✓ MySQL连接检查通过")
    
    # 创建目录
    print("\n4. 创建必要目录...")
    create_directories()
    print("   ✓ 目录创建完成")
    
    print("\n" + "=" * 50)
    print("系统检查完成，正在启动应用...")
    print("=" * 50)
    
    # 启动应用
    try:
        from app import app
        print("\n启动信息:")
        print("- 访问地址: http://localhost:5000")
        print("- 管理员账户: admin")
        print("- 管理员密码: admin123")
        print("- 按 Ctrl+C 停止服务")
        print("\n" + "=" * 50)
        
        app.run(debug=True, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n系统已停止")
    except Exception as e:
        print(f"\n启动失败: {e}")
        return False
    
    return True

if __name__ == '__main__':
    success = main()
    if not success:
        print("\n启动失败，请检查上述错误信息")
        input("按回车键退出...")
        sys.exit(1)
